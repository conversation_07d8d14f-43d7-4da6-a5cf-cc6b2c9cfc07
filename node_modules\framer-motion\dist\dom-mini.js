!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).Motion={})}(this,(function(t){"use strict";const e=t=>t;let n=e;function i(t){let e;return()=>(void 0===e&&(e=t()),e)}const a=(t,e,n)=>{const i=e-t;return 0===i?1:(n-t)/i},s=t=>1e3*t,o=t=>t/1e3,r=i(()=>void 0!==window.ScrollTimeline);class u extends class{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}get finished(){return Promise.all(this.animations.map(t=>"finished"in t?t.finished:t))}getAll(t){return this.animations[0][t]}setAll(t,e){for(let n=0;n<this.animations.length;n++)this.animations[n][t]=e}attachTimeline(t,e){const n=this.animations.map(n=>r()&&n.attachTimeline?n.attachTimeline(t):"function"==typeof e?e(n):void 0);return()=>{n.forEach((t,e)=>{t&&t(),this.animations[e].stop()})}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let e=0;e<this.animations.length;e++)t=Math.max(t,this.animations[e].duration);return t}runAll(t){this.animations.forEach(e=>e[t]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}{then(t,e){return Promise.all(this.animations).then(t).catch(e)}}function l(t,e=100,n){const i=n({...t,keyframes:[0,e]}),a=Math.min(function(t){let e=0,n=t.next(e);for(;!n.done&&e<2e4;)e+=50,n=t.next(e);return e>=2e4?1/0:e}(i),2e4);return{type:"keyframes",ease:t=>i.next(a*t).value/e,duration:o(a)}}function c(t){return"function"==typeof t}const h={linearEasing:void 0};function f(t,e){const n=i(t);return()=>{var t;return null!==(t=h[e])&&void 0!==t?t:n()}}const m=f(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),d=([t,e,n,i])=>`cubic-bezier(${t}, ${e}, ${n}, ${i})`,p={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:d([0,.65,.55,1]),circOut:d([.55,0,1,.45]),backIn:d([.31,.01,.66,-.59]),backOut:d([.33,1.53,.69,.99])};function y(t,e){return t?"function"==typeof t&&m()?((t,e,n=10)=>{let i="";const s=Math.max(Math.round(e/n),2);for(let e=0;e<s;e++)i+=t(a(0,s-1,e))+", ";return`linear(${i.substring(0,i.length-2)})`})(t,e):(t=>Array.isArray(t)&&"number"==typeof t[0])(t)?d(t):Array.isArray(t)?t.map(t=>y(t,e)||p.easeOut):p[t]:void 0}function g(t,e,n){var i;if(t instanceof Element)return[t];if("string"==typeof t){let a=document;e&&(a=e.current);const s=null!==(i=null==n?void 0:n[t])&&void 0!==i?i:a.querySelectorAll(t);return s?Array.from(s):[]}return Array.from(t)}function v(t,e){return n=t,Array.isArray(n)&&"number"!=typeof n[0]?t[((t,e,n)=>{const i=e-t;return((n-t)%i+i)%i+t})(0,t.length,e)]:t;var n}const A=(t,e,n)=>t+(e-t)*n;function b(t,e){const n=t[t.length-1];for(let i=1;i<=e;i++){const s=a(0,e,i);t.push(A(n,1,s))}}function T(t){const e=[0];return b(e,t.length-1),e}function x(t,e,n,i){return"string"==typeof t&&function(t){return"object"==typeof t&&!Array.isArray(t)}(e)?g(t,n,i):t instanceof NodeList?Array.from(t):Array.isArray(t)?t:[t]}function P(t,e,n){return t*(e+1)}function w(t,e,n,i){var a;return"number"==typeof e?e:e.startsWith("-")||e.startsWith("+")?Math.max(0,t+parseFloat(e)):"<"===e?n:null!==(a=i.get(e))&&void 0!==a?a:t}function k(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}function M(t,e,n,i,a,s){!function(t,e,n){for(let i=0;i<t.length;i++){const a=t[i];a.at>e&&a.at<n&&(k(t,a),i--)}}(t,a,s);for(let o=0;o<e.length;o++)t.push({value:e[o],at:A(a,s,i[o]),easing:v(n,o)})}function O(t,e){for(let n=0;n<t.length;n++)t[n]=t[n]/(e+1)}function R(t,e){return t.at===e.at?null===t.value?1:null===e.value?-1:0:t.at-e.at}function W(t,e){return!e.has(t)&&e.set(t,{}),e.get(t)}function E(t,e){return e[t]||(e[t]=[]),e[t]}function F(t){return Array.isArray(t)?t:[t]}function B(t,e){return t&&t[e]?{...t,...t[e]}:{...t}}const S=t=>"number"==typeof t,$=t=>t.every(S);const I=(t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}))("px"),L={borderWidth:I,borderTopWidth:I,borderRightWidth:I,borderBottomWidth:I,borderLeftWidth:I,borderRadius:I,radius:I,borderTopLeftRadius:I,borderTopRightRadius:I,borderBottomRightRadius:I,borderBottomLeftRadius:I,width:I,maxWidth:I,height:I,maxHeight:I,top:I,right:I,bottom:I,left:I,padding:I,paddingTop:I,paddingRight:I,paddingBottom:I,paddingLeft:I,margin:I,marginTop:I,marginRight:I,marginBottom:I,marginLeft:I,backgroundPositionX:I,backgroundPositionY:I},V=t=>null!==t;function j(t,e,n){t.style.setProperty("--"+e,n)}function q(t,e,n){t.style[e]=n}const C=i(()=>{try{document.createElement("div").animate({opacity:[1]})}catch(t){return!1}return!0}),N=i(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),z=new WeakMap;function D(t){const e=z.get(t)||new Map;return z.set(t,e),z.get(t)}class H extends class{constructor(t){this.animation=t}get duration(){var t,e,n;const i=(null===(e=null===(t=this.animation)||void 0===t?void 0:t.effect)||void 0===e?void 0:e.getComputedTiming().duration)||(null===(n=this.options)||void 0===n?void 0:n.duration)||300;return o(Number(i))}get time(){var t;return this.animation?o((null===(t=this.animation)||void 0===t?void 0:t.currentTime)||0):0}set time(t){this.animation&&(this.animation.currentTime=s(t))}get speed(){return this.animation?this.animation.playbackRate:1}set speed(t){this.animation&&(this.animation.playbackRate=t)}get state(){return this.animation?this.animation.playState:"finished"}get startTime(){return this.animation?this.animation.startTime:null}get finished(){return this.animation?this.animation.finished:Promise.resolve()}play(){this.animation&&this.animation.play()}pause(){this.animation&&this.animation.pause()}stop(){this.animation&&"idle"!==this.state&&"finished"!==this.state&&(this.animation.commitStyles&&this.animation.commitStyles(),this.cancel())}flatten(){var t;this.animation&&(null===(t=this.animation.effect)||void 0===t||t.updateTiming({easing:"linear"}))}attachTimeline(t){return this.animation&&function(t,e){t.timeline=e,t.onfinish=null}(this.animation,t),e}complete(){this.animation&&this.animation.finish()}cancel(){try{this.animation&&this.animation.cancel()}catch(t){}}}{constructor(t,e,i,a){const o=e.startsWith("--");n("string"!=typeof a.type);const r=D(t).get(e);r&&r.stop();if(Array.isArray(i)||(i=[i]),function(t,e,n){for(let i=0;i<e.length;i++)null===e[i]&&(e[i]=0===i?n():e[i-1]),"number"==typeof e[i]&&L[t]&&(e[i]=L[t].transform(e[i]));!C()&&e.length<2&&e.unshift(n())}(e,i,()=>e.startsWith("--")?t.style.getPropertyValue(e):window.getComputedStyle(t)[e]),c(a.type)){const t=l(a,100,a.type);a.ease=m()?t.ease:"easeOut",a.duration=s(t.duration),a.type="keyframes"}else a.ease=a.ease||"easeOut";const u=()=>{this.setValue(t,e,function(t,{repeat:e,repeatType:n="loop"},i){const a=t.filter(V),s=e&&"loop"!==n&&e%2==1?0:a.length-1;return s&&void 0!==i?i:a[s]}(i,a)),this.cancel(),this.resolveFinishedPromise()},h=()=>{this.setValue=o?j:q,this.options=a,this.updateFinishedPromise(),this.removeAnimation=()=>{const n=z.get(t);n&&n.delete(e)}};N()?(super(function(t,e,n,{delay:i=0,duration:a=300,repeat:s=0,repeatType:o="loop",ease:r="easeInOut",times:u}={}){const l={[e]:n};u&&(l.offset=u);const c=y(r,a);return Array.isArray(c)&&(l.easing=c),t.animate(l,{delay:i,duration:a,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:s+1,direction:"reverse"===o?"alternate":"normal"})}(t,e,i,a)),h(),!1===a.autoplay&&this.animation.pause(),this.animation.onfinish=u,D(t).set(e,this)):(super(),h(),u())}then(t,e){return this.currentFinishedPromise.then(t,e)}updateFinishedPromise(){this.currentFinishedPromise=new Promise(t=>{this.resolveFinishedPromise=t})}play(){"finished"===this.state&&this.updateFinishedPromise(),super.play()}cancel(){this.removeAnimation(),super.cancel()}}function X(t,e,n,i){const a=g(t,i),o=a.length,r=[];for(let t=0;t<o;t++){const i=a[t],c={...n};"function"==typeof c.delay&&(c.delay=c.delay(t,o));for(const t in e){const n=e[t],a={...(u=c,l=t,u?u[l]||u.default||u:void 0)};a.duration=a.duration?s(a.duration):a.duration,a.delay=s(a.delay||0),r.push(new H(i,t,n,a))}}var u,l;return r}const Y=(t=>function(e,n,i){return new u(X(e,n,i,t))})();t.animate=Y,t.animateSequence=function(t,e){const n=[];return function(t,{defaultTransition:e={},...n}={},i,o){const r=e.duration||.3,u=new Map,h=new Map,f={},m=new Map;let d=0,p=0,y=0;for(let n=0;n<t.length;n++){const a=t[n];if("string"==typeof a){m.set(a,p);continue}if(!Array.isArray(a)){m.set(a.name,w(p,a.at,d,m));continue}let[u,A,k={}]=a;void 0!==k.at&&(p=w(p,k.at,d,m));let R=0;const S=(t,n,i,a=0,u=0)=>{const h=F(t),{delay:f=0,times:m=T(h),type:d="keyframes",repeat:g,repeatType:A,repeatDelay:x=0,...w}=n;let{ease:k=e.ease||"easeOut",duration:W}=n;const E="function"==typeof f?f(a,u):f,B=h.length,S=c(d)?d:null==o?void 0:o[d];if(B<=2&&S){let t=100;if(2===B&&$(h)){const e=h[1]-h[0];t=Math.abs(e)}const e={...w};void 0!==W&&(e.duration=s(W));const n=l(e,t,S);k=n.ease,W=n.duration}null!=W||(W=r);const I=p+E;1===m.length&&0===m[0]&&(m[1]=1);const L=m.length-h.length;if(L>0&&b(m,L),1===h.length&&h.unshift(null),g){W=P(W,g);const t=[...h],e=[...m];k=Array.isArray(k)?[...k]:[k];const n=[...k];for(let i=0;i<g;i++){h.push(...t);for(let a=0;a<t.length;a++)m.push(e[a]+(i+1)),k.push(0===a?"linear":v(n,a-1))}O(m,g)}const V=I+W;M(i,h,k,m,I,V),R=Math.max(E+W,R),y=Math.max(V,y)};if(g=u,Boolean(g&&g.getVelocity)){S(A,k,E("default",W(u,h)))}else{const t=x(u,A,i,f),e=t.length;for(let n=0;n<e;n++){A=A,k=k;const i=W(t[n],h);for(const t in A)S(A[t],B(k,t),E(t,i),n,e)}}d=p,p+=R}var g;return h.forEach((t,i)=>{for(const s in t){const o=t[s];o.sort(R);const r=[],l=[],c=[];for(let t=0;t<o.length;t++){const{at:e,value:n,easing:i}=o[t];r.push(n),l.push(a(0,y,e)),c.push(i||"easeOut")}0!==l[0]&&(l.unshift(0),r.unshift(r[0]),c.unshift("easeInOut")),1!==l[l.length-1]&&(l.push(1),r.push(null)),u.has(i)||u.set(i,{keyframes:{},transition:{}});const h=u.get(i);h.keyframes[s]=r,h.transition[s]={...e,duration:y,ease:c,times:l,...n}}}),u}(t,e).forEach(({keyframes:t,transition:e},i)=>{n.push(...X(i,t,e))}),new u(n)}}));
