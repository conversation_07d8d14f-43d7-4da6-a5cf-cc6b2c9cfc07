'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { Send, X, Mail, User, MapPin, MessageSquare } from 'lucide-react'

interface ContactFormProps {
  onClose?: () => void
  initialMessage?: string
  initialZipCode?: string
}

interface FormData {
  name: string
  email: string
  zipCode: string
  message: string
  category: string
}

export default function ContactForm({ 
  onClose, 
  initialMessage = '', 
  initialZipCode = '' 
}: ContactFormProps) {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    zipCode: initialZipCode,
    message: initialMessage,
    category: 'feedback'
  })

  const [errors, setErrors] = useState<Partial<FormData>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)

  const categories = [
    { value: 'feedback', label: 'General Feedback' },
    { value: 'bug', label: 'Bug Report' },
    { value: 'feature', label: 'Feature Request' },
    { value: 'prediction', label: 'Prediction Question' },
    { value: 'technical', label: 'Technical Issue' },
    { value: 'other', label: 'Other' }
  ]

  const validateForm = (): boolean => {
    const newErrors: Partial<FormData> = {}

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required'
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required'
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address'
    }

    if (!formData.message.trim()) {
      newErrors.message = 'Message is required'
    } else if (formData.message.trim().length < 10) {
      newErrors.message = 'Message must be at least 10 characters long'
    }

    if (formData.zipCode && !/^(\d{5}|[A-Za-z]\d[A-Za-z] \d[A-Za-z]\d)$/.test(formData.zipCode)) {
      newErrors.zipCode = 'Please enter a valid zip code'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)

    try {
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      if (response.ok) {
        setIsSubmitted(true)
        // Reset form after successful submission
        setTimeout(() => {
          setFormData({
            name: '',
            email: '',
            zipCode: '',
            message: '',
            category: 'feedback'
          })
          setIsSubmitted(false)
          onClose?.()
        }, 3000)
      } else {
        throw new Error('Failed to send message')
      }
    } catch (error) {
      console.error('Error sending message:', error)
      alert('Failed to send message. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }
  }

  if (isSubmitted) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="p-8 text-center"
      >
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.2 }}
          className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4"
        >
          <Send className="w-8 h-8 text-green-600" />
        </motion.div>
        <h3 className="text-2xl font-bold text-gray-800 mb-2">
          Message Sent!
        </h3>
        <p className="text-gray-600 mb-4">
          Thank you for your feedback. We'll get back to you as soon as possible.
        </p>
        <p className="text-sm text-gray-500">
          This window will close automatically...
        </p>
      </motion.div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="p-8"
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-gray-800">
          Send Feedback
        </h2>
        {onClose && (
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        )}
      </div>

      {/* Contact Info */}
      <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
        <h3 className="font-semibold text-blue-800 mb-2">
          Contact David Sukhin:
        </h3>
        <p className="text-sm text-blue-700 mb-2">
          If you use this form, I will get back to you with a detailed response to your question or comment as soon as I can.
        </p>
        <p className="text-sm text-blue-700 mb-2">
          If you include a valid email address, I guarantee to get back to you!
        </p>
        <p className="text-sm text-blue-700">
          For quicker responses about current predictions, tweet to{' '}
          <a href="https://twitter.com/SnowDayCalc" className="underline" target="_blank" rel="noopener noreferrer">
            @SnowDayCalc
          </a>
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Name */}
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
            <User className="w-4 h-4 inline mr-2" />
            Name
          </label>
          <input
            type="text"
            id="name"
            className={`form-input ${errors.name ? 'border-red-500' : ''}`}
            placeholder="Your name"
            value={formData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
          />
          {errors.name && (
            <p className="mt-1 text-sm text-red-600">{errors.name}</p>
          )}
        </div>

        {/* Email */}
        <div>
          <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
            <Mail className="w-4 h-4 inline mr-2" />
            Email
          </label>
          <input
            type="email"
            id="email"
            className={`form-input ${errors.email ? 'border-red-500' : ''}`}
            placeholder="<EMAIL>"
            value={formData.email}
            onChange={(e) => handleInputChange('email', e.target.value)}
          />
          {errors.email && (
            <p className="mt-1 text-sm text-red-600">{errors.email}</p>
          )}
        </div>

        {/* Zip Code */}
        <div>
          <label htmlFor="zipCode" className="block text-sm font-medium text-gray-700 mb-2">
            <MapPin className="w-4 h-4 inline mr-2" />
            Zip Code (Optional)
          </label>
          <input
            type="text"
            id="zipCode"
            className={`form-input ${errors.zipCode ? 'border-red-500' : ''}`}
            placeholder="12345 or A1A 1A1"
            value={formData.zipCode}
            onChange={(e) => handleInputChange('zipCode', e.target.value.toUpperCase())}
          />
          {errors.zipCode && (
            <p className="mt-1 text-sm text-red-600">{errors.zipCode}</p>
          )}
        </div>

        {/* Category */}
        <div>
          <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-2">
            Category
          </label>
          <select
            id="category"
            className="form-select"
            value={formData.category}
            onChange={(e) => handleInputChange('category', e.target.value)}
          >
            {categories.map((category) => (
              <option key={category.value} value={category.value}>
                {category.label}
              </option>
            ))}
          </select>
        </div>

        {/* Message */}
        <div>
          <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
            <MessageSquare className="w-4 h-4 inline mr-2" />
            Message
          </label>
          <textarea
            id="message"
            rows={6}
            className={`form-input resize-none ${errors.message ? 'border-red-500' : ''}`}
            placeholder="Please write your message here..."
            value={formData.message}
            onChange={(e) => handleInputChange('message', e.target.value)}
          />
          {errors.message && (
            <p className="mt-1 text-sm text-red-600">{errors.message}</p>
          )}
          <p className="mt-1 text-xs text-gray-500">
            Minimum 10 characters
          </p>
        </div>

        {/* Submit Button */}
        <div className="flex flex-col sm:flex-row gap-4">
          {onClose && (
            <button
              type="button"
              onClick={onClose}
              className="btn-secondary flex-1"
            >
              Close
            </button>
          )}
          <motion.button
            type="submit"
            disabled={isSubmitting}
            className={`btn-primary flex-1 ${isSubmitting ? 'opacity-50 cursor-not-allowed' : ''}`}
            whileHover={{ scale: isSubmitting ? 1 : 1.05 }}
            whileTap={{ scale: isSubmitting ? 1 : 0.95 }}
          >
            {isSubmitting ? (
              <div className="flex items-center justify-center space-x-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Sending...</span>
              </div>
            ) : (
              <div className="flex items-center justify-center space-x-2">
                <Send className="w-4 h-4" />
                <span>Send Message</span>
              </div>
            )}
          </motion.button>
        </div>
      </form>
    </motion.div>
  )
}
