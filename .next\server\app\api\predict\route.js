/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/predict/route";
exports.ids = ["app/api/predict/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fpredict%2Froute&page=%2Fapi%2Fpredict%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpredict%2Froute.ts&appDir=C%3A%5CUsers%5CAdministrator%5COneDrive%5CDesktop%5Csnow%20au%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdministrator%5COneDrive%5CDesktop%5Csnow%20au&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fpredict%2Froute&page=%2Fapi%2Fpredict%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpredict%2Froute.ts&appDir=C%3A%5CUsers%5CAdministrator%5COneDrive%5CDesktop%5Csnow%20au%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdministrator%5COneDrive%5CDesktop%5Csnow%20au&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Administrator_OneDrive_Desktop_snow_au_src_app_api_predict_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/predict/route.ts */ \"(rsc)/./src/app/api/predict/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/predict/route\",\n        pathname: \"/api/predict\",\n        filename: \"route\",\n        bundlePath: \"app/api/predict/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\snow au\\\\src\\\\app\\\\api\\\\predict\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Administrator_OneDrive_Desktop_snow_au_src_app_api_predict_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZwcmVkaWN0JTJGcm91dGUmcGFnZT0lMkZhcGklMkZwcmVkaWN0JTJGcm91dGUmYXBwUGF0aHM9JnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGYXBpJTJGcHJlZGljdCUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDVXNlcnMlNUNBZG1pbmlzdHJhdG9yJTVDT25lRHJpdmUlNUNEZXNrdG9wJTVDc25vdyUyMGF1JTVDc3JjJTVDYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj1DJTNBJTVDVXNlcnMlNUNBZG1pbmlzdHJhdG9yJTVDT25lRHJpdmUlNUNEZXNrdG9wJTVDc25vdyUyMGF1JmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUErRjtBQUN2QztBQUNxQjtBQUNzQztBQUNuSDtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IseUdBQW1CO0FBQzNDO0FBQ0EsY0FBYyxrRUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsWUFBWTtBQUNaLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSxRQUFRLHNEQUFzRDtBQUM5RDtBQUNBLFdBQVcsNEVBQVc7QUFDdEI7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUMwRjs7QUFFMUYiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBBcHBSb3V0ZVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUtbW9kdWxlcy9hcHAtcm91dGUvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1raW5kXCI7XG5pbXBvcnQgeyBwYXRjaEZldGNoIGFzIF9wYXRjaEZldGNoIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvbGliL3BhdGNoLWZldGNoXCI7XG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiQzpcXFxcVXNlcnNcXFxcQWRtaW5pc3RyYXRvclxcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXHNub3cgYXVcXFxcc3JjXFxcXGFwcFxcXFxhcGlcXFxccHJlZGljdFxcXFxyb3V0ZS50c1wiO1xuLy8gV2UgaW5qZWN0IHRoZSBuZXh0Q29uZmlnT3V0cHV0IGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCBuZXh0Q29uZmlnT3V0cHV0ID0gXCJcIlxuY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUm91dGVSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1JPVVRFLFxuICAgICAgICBwYWdlOiBcIi9hcGkvcHJlZGljdC9yb3V0ZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvYXBpL3ByZWRpY3RcIixcbiAgICAgICAgZmlsZW5hbWU6IFwicm91dGVcIixcbiAgICAgICAgYnVuZGxlUGF0aDogXCJhcHAvYXBpL3ByZWRpY3Qvcm91dGVcIlxuICAgIH0sXG4gICAgcmVzb2x2ZWRQYWdlUGF0aDogXCJDOlxcXFxVc2Vyc1xcXFxBZG1pbmlzdHJhdG9yXFxcXE9uZURyaXZlXFxcXERlc2t0b3BcXFxcc25vdyBhdVxcXFxzcmNcXFxcYXBwXFxcXGFwaVxcXFxwcmVkaWN0XFxcXHJvdXRlLnRzXCIsXG4gICAgbmV4dENvbmZpZ091dHB1dCxcbiAgICB1c2VybGFuZFxufSk7XG4vLyBQdWxsIG91dCB0aGUgZXhwb3J0cyB0aGF0IHdlIG5lZWQgdG8gZXhwb3NlIGZyb20gdGhlIG1vZHVsZS4gVGhpcyBzaG91bGRcbi8vIGJlIGVsaW1pbmF0ZWQgd2hlbiB3ZSd2ZSBtb3ZlZCB0aGUgb3RoZXIgcm91dGVzIHRvIHRoZSBuZXcgZm9ybWF0LiBUaGVzZVxuLy8gYXJlIHVzZWQgdG8gaG9vayBpbnRvIHRoZSByb3V0ZS5cbmNvbnN0IHsgd29ya0FzeW5jU3RvcmFnZSwgd29ya1VuaXRBc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzIH0gPSByb3V0ZU1vZHVsZTtcbmZ1bmN0aW9uIHBhdGNoRmV0Y2goKSB7XG4gICAgcmV0dXJuIF9wYXRjaEZldGNoKHtcbiAgICAgICAgd29ya0FzeW5jU3RvcmFnZSxcbiAgICAgICAgd29ya1VuaXRBc3luY1N0b3JhZ2VcbiAgICB9KTtcbn1cbmV4cG9ydCB7IHJvdXRlTW9kdWxlLCB3b3JrQXN5bmNTdG9yYWdlLCB3b3JrVW5pdEFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MsIHBhdGNoRmV0Y2gsICB9O1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcm91dGUuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fpredict%2Froute&page=%2Fapi%2Fpredict%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpredict%2Froute.ts&appDir=C%3A%5CUsers%5CAdministrator%5COneDrive%5CDesktop%5Csnow%20au%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdministrator%5COneDrive%5CDesktop%5Csnow%20au&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/predict/route.ts":
/*!**************************************!*\
  !*** ./src/app/api/predict/route.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_weather__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/weather */ \"(rsc)/./src/lib/weather.ts\");\n/* harmony import */ var _lib_prediction__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/prediction */ \"(rsc)/./src/lib/prediction.ts\");\n\n\n\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { formData, manualData } = body;\n        if (!formData || !formData.zipCode) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Form data with zip code is required'\n            }, {\n                status: 400\n            });\n        }\n        // Get weather data\n        let weatherData;\n        if (true) {\n            try {\n                weatherData = await _lib_weather__WEBPACK_IMPORTED_MODULE_1__.weatherService.getCurrentWeather(formData.zipCode);\n            } catch (error) {\n                console.warn('Weather API failed, using mock data:', error);\n                weatherData = await _lib_weather__WEBPACK_IMPORTED_MODULE_1__.weatherService.getMockWeatherData(formData.zipCode);\n            }\n        } else {}\n        // Calculate predictions\n        const predictions = _lib_prediction__WEBPACK_IMPORTED_MODULE_2__.snowDayPredictor.calculateMultiDayPredictions(weatherData, formData, manualData);\n        // Log prediction for analytics (in a real app, you'd save this to a database)\n        console.log('Prediction generated:', {\n            zipCode: formData.zipCode,\n            schoolType: formData.schoolType,\n            snowDaysUsed: formData.snowDays,\n            predictions: predictions.map((p)=>({\n                    date: p.date,\n                    percentage: p.percentage\n                })),\n            timestamp: new Date().toISOString()\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                predictions,\n                weatherData,\n                formData,\n                manualData: manualData || null\n            },\n            timestamp: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error('Prediction API error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to generate prediction',\n            message: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\n// GET endpoint for sharing predictions\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const zipCode = searchParams.get('zipcode');\n        const prediction = searchParams.get('prediction');\n        const date = searchParams.get('date');\n        if (!zipCode || !prediction) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Zip code and prediction are required'\n            }, {\n                status: 400\n            });\n        }\n        // Generate share data\n        const shareData = {\n            zipCode,\n            prediction: parseInt(prediction),\n            date: date || new Date().toISOString().split('T')[0],\n            url: `${request.nextUrl.origin}/prediction?zipcode=${zipCode}&prediction=${prediction}`,\n            text: `The Snow Day Calculator says there is a ${prediction}% chance of a snow day for ${zipCode}!`,\n            hashtags: [\n                'SnowDay',\n                'Weather',\n                'School'\n            ]\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: shareData,\n            timestamp: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error('Share data API error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to generate share data',\n            message: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/predict/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prediction.ts":
/*!*******************************!*\
  !*** ./src/lib/prediction.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   snowDayPredictor: () => (/* binding */ snowDayPredictor)\n/* harmony export */ });\nclass SnowDayPredictor {\n    // Base prediction algorithm based on original Snow Day Calculator logic\n    calculateSnowDayChance(weatherData, formData, forecastDay, manualData) {\n        const factors = {\n            temperature: 0,\n            snowfall: 0,\n            windSpeed: 0,\n            timing: 0,\n            schoolType: 0,\n            snowDaysUsed: 0,\n            extraFactors: 0,\n            total: 0\n        };\n        // Use manual data if provided, otherwise use forecast data\n        const temp = manualData?.temperature ?? forecastDay.tempLow;\n        const snow = manualData?.snowfall ?? forecastDay.snowfall;\n        const wind = manualData?.windSpeed ?? forecastDay.windSpeed;\n        const precipChance = forecastDay.precipitationChance;\n        // Temperature factor (optimal range: 15-32°F)\n        factors.temperature = this.calculateTemperatureFactor(temp);\n        // Snowfall factor (most important factor)\n        factors.snowfall = this.calculateSnowfallFactor(snow, precipChance);\n        // Wind factor (higher wind = more drifting = higher chance)\n        factors.windSpeed = this.calculateWindFactor(wind);\n        // Timing factor (overnight storms are best)\n        factors.timing = this.calculateTimingFactor(manualData?.stormTiming);\n        // School type factor\n        factors.schoolType = this.calculateSchoolTypeFactor(formData.schoolType);\n        // Snow days already used factor (more used = less likely to close)\n        factors.snowDaysUsed = this.calculateSnowDaysUsedFactor(formData.snowDays);\n        // Extra factors from user input\n        factors.extraFactors = formData.extraFactors * -5 // Negative because lower is better\n        ;\n        // Ice conditions bonus (if manual data)\n        if (manualData?.iceConditions) {\n            factors.extraFactors += 15;\n        }\n        // Calculate total score\n        factors.total = factors.temperature + factors.snowfall + factors.windSpeed + factors.timing + factors.schoolType + factors.snowDaysUsed + factors.extraFactors;\n        // Convert score to percentage (0-100)\n        let percentage = Math.max(0, Math.min(100, factors.total));\n        // Apply some randomness to make it more realistic\n        percentage = Math.round(percentage + (Math.random() - 0.5) * 5);\n        // Generate message based on percentage\n        const message = this.generateMessage(percentage, snow, temp);\n        // Generate share data\n        const shareData = this.generateShareData(formData.zipCode, percentage, forecastDay.dayOfWeek);\n        return {\n            date: forecastDay.date,\n            dayOfWeek: forecastDay.dayOfWeek,\n            percentage: Math.max(0, Math.min(99, percentage)),\n            message,\n            factors,\n            shareData\n        };\n    }\n    calculateTemperatureFactor(temp) {\n        if (temp > 35) return 0 // Too warm for snow to stick\n        ;\n        if (temp < 10) return 5 // Very cold, but may be too cold for heavy snow\n        ;\n        if (temp >= 25 && temp <= 32) return 20 // Optimal range\n        ;\n        if (temp >= 15 && temp < 25) return 15 // Good range\n        ;\n        return 10 // Marginal\n        ;\n    }\n    calculateSnowfallFactor(snowfall, precipChance) {\n        let factor = 0;\n        // Base snowfall factor\n        if (snowfall >= 6) factor = 40;\n        else if (snowfall >= 4) factor = 35;\n        else if (snowfall >= 2) factor = 25;\n        else if (snowfall >= 1) factor = 15;\n        else if (snowfall >= 0.5) factor = 8;\n        else factor = 0;\n        // Adjust for precipitation chance\n        factor *= precipChance / 100;\n        return Math.round(factor);\n    }\n    calculateWindFactor(windSpeed) {\n        if (windSpeed >= 25) return 10 // High winds create drifting\n        ;\n        if (windSpeed >= 15) return 5;\n        return 0;\n    }\n    calculateTimingFactor(timing) {\n        switch(timing){\n            case 'overnight':\n                return 15 // Best timing\n                ;\n            case 'before-school':\n                return 10;\n            case 'during-school':\n                return 5;\n            case 'after-school':\n                return 0;\n            default:\n                return 8 // Default assumption for overnight\n                ;\n        }\n    }\n    calculateSchoolTypeFactor(schoolType) {\n        const multiplier = this.schoolTypeMultipliers[schoolType] || 1.0;\n        return Math.round((multiplier - 1.0) * 20) // Convert to additive factor\n        ;\n    }\n    calculateSnowDaysUsedFactor(snowDaysUsed) {\n        // More snow days used = less likely to close\n        return Math.max(-15, -snowDaysUsed * 2);\n    }\n    generateMessage(percentage, snowfall, temperature) {\n        if (percentage === 0 || snowfall === 0) {\n            return \"No Snow Storm. Sorry!\";\n        }\n        if (percentage < 20) {\n            return \"Little to no chance of anything, but possible.\";\n        } else if (percentage < 55) {\n            return \"Low chance, but keep your fingers crossed!\";\n        } else if (percentage < 75) {\n            return \"Delay Likely.\";\n        } else if (percentage < 87) {\n            return \"Possibility of No School.\";\n        } else {\n            return \"No School or Possible Early Dismissal.\";\n        }\n    }\n    generateShareData(zipCode, percentage, dayOfWeek) {\n        const text = `The @SnowDayCalc says there is a ${percentage}% chance of a #SnowDay this ${dayOfWeek} for ${zipCode}!`;\n        const url = `${window.location.origin}/prediction?zipcode=${zipCode}&prediction=${percentage}`;\n        return {\n            url,\n            text,\n            hashtags: [\n                'SnowDay',\n                'Weather',\n                'School'\n            ]\n        };\n    }\n    // Calculate predictions for multiple days\n    calculateMultiDayPredictions(weatherData, formData, manualData) {\n        const predictions = [];\n        // If manual data is provided, create a single prediction for tomorrow\n        if (manualData) {\n            const tomorrow = new Date();\n            tomorrow.setDate(tomorrow.getDate() + 1);\n            const mockForecastDay = {\n                date: tomorrow.toISOString().split('T')[0],\n                dayOfWeek: tomorrow.toLocaleDateString('en-US', {\n                    weekday: 'long'\n                }),\n                tempHigh: manualData.temperature + 5,\n                tempLow: manualData.temperature,\n                precipitation: manualData.snowfall * 0.1,\n                snowfall: manualData.snowfall,\n                windSpeed: manualData.windSpeed,\n                conditions: manualData.snowfall > 0 ? 'Snow' : 'Clear',\n                precipitationChance: manualData.snowfall > 0 ? 80 : 20\n            };\n            predictions.push(this.calculateSnowDayChance(weatherData, formData, mockForecastDay, manualData));\n        } else {\n            // Use forecast data for automatic predictions\n            weatherData.forecast.forEach((forecastDay)=>{\n                // Skip weekends (Saturday = 6, Sunday = 0)\n                const date = new Date(forecastDay.date);\n                const dayOfWeek = date.getDay();\n                if (dayOfWeek !== 0 && dayOfWeek !== 6) {\n                    predictions.push(this.calculateSnowDayChance(weatherData, formData, forecastDay));\n                }\n            });\n        }\n        return predictions;\n    }\n    constructor(){\n        // School type multipliers\n        this.schoolTypeMultipliers = {\n            'public': 1.0,\n            'urban-public': 0.8,\n            'rural-public': 1.2,\n            'private': 0.7,\n            'boarding': 0.3\n        };\n    }\n}\nconst snowDayPredictor = new SnowDayPredictor();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prediction.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/weather.ts":
/*!****************************!*\
  !*** ./src/lib/weather.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   weatherService: () => (/* binding */ weatherService)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(rsc)/./node_modules/axios/lib/axios.js\");\n\nclass WeatherService {\n    constructor(){\n        this.apiKey = \"your_openweathermap_api_key_here\" || 0;\n        this.baseUrl = \"https://api.openweathermap.org/data/2.5\" || 0;\n    }\n    async getLocationFromZipCode(zipCode) {\n        try {\n            // Handle Canadian postal codes\n            const isCanadian = /^[A-Za-z]\\d[A-Za-z] \\d[A-Za-z]\\d$/.test(zipCode);\n            const country = isCanadian ? 'CA' : 'US';\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${this.baseUrl}/weather`, {\n                params: {\n                    zip: `${zipCode},${country}`,\n                    appid: this.apiKey,\n                    units: 'imperial'\n                }\n            });\n            const data = response.data;\n            return {\n                zipCode,\n                city: data.name,\n                state: data.sys.state || '',\n                country: data.sys.country,\n                latitude: data.coord.lat,\n                longitude: data.coord.lon\n            };\n        } catch (error) {\n            console.error('Error fetching location data:', error);\n            throw new Error('Invalid zip code or location not found');\n        }\n    }\n    async getCurrentWeather(zipCode) {\n        try {\n            const location = await this.getLocationFromZipCode(zipCode);\n            // Get current weather\n            const currentResponse = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${this.baseUrl}/weather`, {\n                params: {\n                    lat: location.latitude,\n                    lon: location.longitude,\n                    appid: this.apiKey,\n                    units: 'imperial'\n                }\n            });\n            // Get 5-day forecast\n            const forecastResponse = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${this.baseUrl}/forecast`, {\n                params: {\n                    lat: location.latitude,\n                    lon: location.longitude,\n                    appid: this.apiKey,\n                    units: 'imperial'\n                }\n            });\n            const current = currentResponse.data;\n            const forecast = forecastResponse.data;\n            // Process forecast data for next 2 days\n            const forecastDays = this.processForecastData(forecast.list);\n            return {\n                temperature: Math.round(current.main.temp),\n                humidity: current.main.humidity,\n                windSpeed: Math.round(current.wind.speed),\n                windDirection: current.wind.deg || 0,\n                precipitation: current.rain?.['1h'] || current.snow?.['1h'] || 0,\n                snowfall: current.snow?.['1h'] || 0,\n                visibility: current.visibility ? current.visibility / 1000 : 10,\n                pressure: current.main.pressure,\n                cloudCover: current.clouds.all,\n                conditions: current.weather[0].description,\n                forecast: forecastDays\n            };\n        } catch (error) {\n            console.error('Error fetching weather data:', error);\n            throw new Error('Unable to fetch weather data');\n        }\n    }\n    processForecastData(forecastList) {\n        const days = {};\n        // Group forecast data by date\n        forecastList.forEach((item)=>{\n            const date = new Date(item.dt * 1000).toDateString();\n            if (!days[date]) {\n                days[date] = [];\n            }\n            days[date].push(item);\n        });\n        // Process only next 2 days\n        const sortedDates = Object.keys(days).sort().slice(0, 2);\n        return sortedDates.map((dateStr)=>{\n            const dayData = days[dateStr];\n            const date = new Date(dateStr);\n            // Calculate daily aggregates\n            const temps = dayData.map((d)=>d.main.temp);\n            const precipitations = dayData.map((d)=>(d.rain?.['3h'] || 0) + (d.snow?.['3h'] || 0));\n            const snowfalls = dayData.map((d)=>d.snow?.['3h'] || 0);\n            const winds = dayData.map((d)=>d.wind.speed);\n            const precipChances = dayData.map((d)=>d.pop * 100);\n            return {\n                date: date.toISOString().split('T')[0],\n                dayOfWeek: date.toLocaleDateString('en-US', {\n                    weekday: 'long'\n                }),\n                tempHigh: Math.round(Math.max(...temps)),\n                tempLow: Math.round(Math.min(...temps)),\n                precipitation: Math.round(Math.max(...precipitations) * 10) / 10,\n                snowfall: Math.round(Math.max(...snowfalls) * 10) / 10,\n                windSpeed: Math.round(Math.max(...winds)),\n                conditions: dayData[0].weather[0].description,\n                precipitationChance: Math.round(Math.max(...precipChances))\n            };\n        });\n    }\n    // Fallback method using mock data for development\n    async getMockWeatherData(zipCode) {\n        // Simulate API delay\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        const mockForecast = [\n            {\n                date: new Date(Date.now() + 86400000).toISOString().split('T')[0],\n                dayOfWeek: 'Tomorrow',\n                tempHigh: 28,\n                tempLow: 18,\n                precipitation: 0.8,\n                snowfall: 3.2,\n                windSpeed: 15,\n                conditions: 'Heavy snow',\n                precipitationChance: 85\n            },\n            {\n                date: new Date(Date.now() + 172800000).toISOString().split('T')[0],\n                dayOfWeek: new Date(Date.now() + 172800000).toLocaleDateString('en-US', {\n                    weekday: 'long'\n                }),\n                tempHigh: 32,\n                tempLow: 22,\n                precipitation: 0.3,\n                snowfall: 1.1,\n                windSpeed: 8,\n                conditions: 'Light snow',\n                precipitationChance: 60\n            }\n        ];\n        return {\n            temperature: 25,\n            humidity: 78,\n            windSpeed: 12,\n            windDirection: 270,\n            precipitation: 0.5,\n            snowfall: 2.1,\n            visibility: 2.5,\n            pressure: 1013,\n            cloudCover: 90,\n            conditions: 'Snow',\n            forecast: mockForecast\n        };\n    }\n}\nconst weatherService = new WeatherService();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/weather.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/axios","vendor-chunks/asynckit","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/call-bind-apply-helpers","vendor-chunks/debug","vendor-chunks/get-proto","vendor-chunks/mime-db","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/form-data","vendor-chunks/follow-redirects","vendor-chunks/supports-color","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/mime-types","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/has-flag","vendor-chunks/get-intrinsic","vendor-chunks/es-set-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/delayed-stream","vendor-chunks/combined-stream"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fpredict%2Froute&page=%2Fapi%2Fpredict%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpredict%2Froute.ts&appDir=C%3A%5CUsers%5CAdministrator%5COneDrive%5CDesktop%5Csnow%20au%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdministrator%5COneDrive%5CDesktop%5Csnow%20au&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();