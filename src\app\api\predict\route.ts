import { NextRequest, NextResponse } from 'next/server'
import { weatherService } from '@/lib/weather'
import { snowDayPredictor } from '@/lib/prediction'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { formData, manualData } = body

    if (!formData || !formData.zipCode) {
      return NextResponse.json(
        { error: 'Form data with zip code is required' },
        { status: 400 }
      )
    }

    // Get weather data
    let weatherData
    if (process.env.WEATHER_API_KEY) {
      try {
        weatherData = await weatherService.getCurrentWeather(formData.zipCode)
      } catch (error) {
        console.warn('Weather API failed, using mock data:', error)
        weatherData = await weatherService.getMockWeatherData(formData.zipCode)
      }
    } else {
      console.warn('No weather API key found, using mock data')
      weatherData = await weatherService.getMockWeatherData(formData.zipCode)
    }

    // Calculate predictions
    const predictions = snowDayPredictor.calculateMultiDayPredictions(
      weatherData,
      formData,
      manualData
    )

    // Log prediction for analytics (in a real app, you'd save this to a database)
    console.log('Prediction generated:', {
      zipCode: formData.zipCode,
      schoolType: formData.schoolType,
      snowDaysUsed: formData.snowDays,
      predictions: predictions.map(p => ({ date: p.date, percentage: p.percentage })),
      timestamp: new Date().toISOString()
    })

    return NextResponse.json({
      success: true,
      data: {
        predictions,
        weatherData,
        formData,
        manualData: manualData || null
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Prediction API error:', error)
    return NextResponse.json(
      { 
        error: 'Failed to generate prediction',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// GET endpoint for sharing predictions
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const zipCode = searchParams.get('zipcode')
    const prediction = searchParams.get('prediction')
    const date = searchParams.get('date')

    if (!zipCode || !prediction) {
      return NextResponse.json(
        { error: 'Zip code and prediction are required' },
        { status: 400 }
      )
    }

    // Generate share data
    const shareData = {
      zipCode,
      prediction: parseInt(prediction),
      date: date || new Date().toISOString().split('T')[0],
      url: `${request.nextUrl.origin}/prediction?zipcode=${zipCode}&prediction=${prediction}`,
      text: `The Snow Day Calculator says there is a ${prediction}% chance of a snow day for ${zipCode}!`,
      hashtags: ['SnowDay', 'Weather', 'School']
    }

    return NextResponse.json({
      success: true,
      data: shareData,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Share data API error:', error)
    return NextResponse.json(
      { 
        error: 'Failed to generate share data',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
