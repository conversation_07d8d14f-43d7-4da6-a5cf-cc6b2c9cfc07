'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Menu, X } from 'lucide-react'

interface MobileNavigationProps {
  activeSection: string
  onSectionChange: (section: string) => void
}

const navigationItems = [
  { id: 'calculator', label: 'Calculator' },
  { id: 'about', label: 'About' },
  { id: 'apps', label: 'Apps' },
  { id: 'services', label: 'Services' },
  { id: 'contact', label: 'Contact' },
]

export default function MobileNavigation({ activeSection, onSectionChange }: MobileNavigationProps) {
  const [isOpen, setIsOpen] = useState(false)

  const handleSectionChange = (section: string) => {
    onSectionChange(section)
    setIsOpen(false)
  }

  return (
    <>
      {/* Mobile Menu Button */}
      <div className="md:hidden fixed top-4 left-4 z-50">
        <motion.button
          onClick={() => setIsOpen(!isOpen)}
          className="p-3 bg-orange-gradient text-white rounded-full shadow-lg"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          {isOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
        </motion.button>
      </div>

      {/* Mobile Menu Overlay */}
      <AnimatePresence>
        {isOpen && (
          <>
            {/* Backdrop */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="md:hidden fixed inset-0 bg-black bg-opacity-50 z-40"
              onClick={() => setIsOpen(false)}
            />

            {/* Menu Panel */}
            <motion.div
              initial={{ x: '-100%' }}
              animate={{ x: 0 }}
              exit={{ x: '-100%' }}
              transition={{ type: 'tween', duration: 0.3 }}
              className="md:hidden fixed left-0 top-0 h-full w-80 bg-white shadow-xl z-40 overflow-y-auto"
            >
              <div className="p-6 pt-20">
                <h2 className="text-2xl font-bold text-gray-800 mb-6">
                  Navigation
                </h2>
                
                <nav className="space-y-2">
                  {navigationItems.map((item) => (
                    <motion.button
                      key={item.id}
                      onClick={() => handleSectionChange(item.id)}
                      className={`w-full text-left p-4 rounded-lg transition-colors ${
                        activeSection === item.id
                          ? 'bg-orange-gradient text-white'
                          : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                      }`}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <span className="text-lg font-medium">{item.label}</span>
                    </motion.button>
                  ))}
                </nav>

                {/* Footer */}
                <div className="mt-8 pt-6 border-t border-gray-200">
                  <p className="text-sm text-gray-600 text-center">
                    Snow Day Calculator
                  </p>
                  <p className="text-xs text-gray-500 text-center mt-1">
                    © 2007-2025 David Sukhin
                  </p>
                </div>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </>
  )
}
