!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("react")):"function"==typeof define&&define.amd?define(["exports","react"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).Motion={},t.React)}(this,(function(t,e){"use strict";function n(t){var e=Object.create(null);return t&&Object.keys(t).forEach((function(n){if("default"!==n){var i=Object.getOwnPropertyDescriptor(t,n);Object.defineProperty(e,n,i.get?i:{enumerable:!0,get:function(){return t[n]}})}})),e.default=t,Object.freeze(e)}var i=n(e),s=React,o=Symbol.for("react.element"),r=Symbol.for("react.fragment"),a=Object.prototype.hasOwnProperty,l=s.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,u={key:!0,ref:!0,__self:!0,__source:!0};function c(t,e,n){var i,s={},r=null,c=null;for(i in void 0!==n&&(r=""+n),void 0!==e.key&&(r=""+e.key),void 0!==e.ref&&(c=e.ref),e)a.call(e,i)&&!u.hasOwnProperty(i)&&(s[i]=e[i]);if(t&&t.defaultProps)for(i in e=t.defaultProps)void 0===s[i]&&(s[i]=e[i]);return{$$typeof:o,type:t,key:r,ref:c,props:s,_owner:l.current}}const h=r,d=c,p=c,m=e.createContext({});function f(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}const g=t=>Array.isArray(t);function y(t,e){if(!Array.isArray(e))return!1;const n=e.length;if(n!==t.length)return!1;for(let i=0;i<n;i++)if(e[i]!==t[i])return!1;return!0}function v(t){return"string"==typeof t||Array.isArray(t)}function x(t){const e=[{},{}];return null==t||t.values.forEach((t,n)=>{e[0][n]=t.get(),e[1][n]=t.getVelocity()}),e}function w(t,e,n,i){if("function"==typeof e){const[s,o]=x(i);e=e(void 0!==n?n:t.custom,s,o)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){const[s,o]=x(i);e=e(void 0!==n?n:t.custom,s,o)}return e}function P(t,e,n){const i=t.getProps();return w(i,e,void 0!==n?n:i.custom,t)}const T=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],S=["initial",...T],b=t=>t;let A=b,E=b;function M(t){let e;return()=>(void 0===e&&(e=t()),e)}const C=(t,e,n)=>{const i=e-t;return 0===i?1:(n-t)/i},V=t=>1e3*t,R=t=>t/1e3,D=M(()=>void 0!==window.ScrollTimeline);class k extends class{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}get finished(){return Promise.all(this.animations.map(t=>"finished"in t?t.finished:t))}getAll(t){return this.animations[0][t]}setAll(t,e){for(let n=0;n<this.animations.length;n++)this.animations[n][t]=e}attachTimeline(t,e){const n=this.animations.map(n=>D()&&n.attachTimeline?n.attachTimeline(t):"function"==typeof e?e(n):void 0);return()=>{n.forEach((t,e)=>{t&&t(),this.animations[e].stop()})}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let e=0;e<this.animations.length;e++)t=Math.max(t,this.animations[e].duration);return t}runAll(t){this.animations.forEach(e=>e[t]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}{then(t,e){return Promise.all(this.animations).then(t).catch(e)}}function L(t,e){return t?t[e]||t.default||t:void 0}function B(t){let e=0;let n=t.next(e);for(;!n.done&&e<2e4;)e+=50,n=t.next(e);return e>=2e4?1/0:e}function F(t,e=100,n){const i=n({...t,keyframes:[0,e]}),s=Math.min(B(i),2e4);return{type:"keyframes",ease:t=>i.next(s*t).value/e,duration:R(s)}}function j(t){return"function"==typeof t}function O(t,e){t.timeline=e,t.onfinish=null}const I=t=>Array.isArray(t)&&"number"==typeof t[0],U={linearEasing:void 0};function W(t,e){const n=M(t);return()=>{var t;return null!==(t=U[e])&&void 0!==t?t:n()}}const N=W(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),z=(t,e,n=10)=>{let i="";const s=Math.max(Math.round(e/n),2);for(let e=0;e<s;e++)i+=t(C(0,s-1,e))+", ";return`linear(${i.substring(0,i.length-2)})`};function $(t){return Boolean("function"==typeof t&&N()||!t||"string"==typeof t&&(t in Y||N())||I(t)||Array.isArray(t)&&t.every($))}const H=([t,e,n,i])=>`cubic-bezier(${t}, ${e}, ${n}, ${i})`,Y={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:H([0,.65,.55,1]),circOut:H([.55,0,1,.45]),backIn:H([.31,.01,.66,-.59]),backOut:H([.33,1.53,.69,.99])};const X={x:!1,y:!1};function K(){return X.x||X.y}function G(t,e,n){var i;if(t instanceof Element)return[t];if("string"==typeof t){let s=document;e&&(s=e.current);const o=null!==(i=null==n?void 0:n[t])&&void 0!==i?i:s.querySelectorAll(t);return o?Array.from(o):[]}return Array.from(t)}function _(t,e){const n=G(t),i=new AbortController;return[n,{passive:!0,...e,signal:i.signal},()=>i.abort()]}function q(t){return e=>{"touch"===e.pointerType||K()||t(e)}}const Z=(t,e)=>!!e&&(t===e||Z(t,e.parentElement)),J=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary,Q=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);const tt=new WeakSet;function et(t){return e=>{"Enter"===e.key&&t(e)}}function nt(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}function it(t){return J(t)&&!K()}function st(t,e,n={}){const[i,s,o]=_(t,n),r=t=>{const i=t.currentTarget;if(!it(t)||tt.has(i))return;tt.add(i);const o=e(t),r=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),it(t)&&tt.has(i)&&(tt.delete(i),"function"==typeof o&&o(t,{success:e}))},a=t=>{r(t,n.useGlobalTarget||Z(i,t.target))},l=t=>{r(t,!1)};window.addEventListener("pointerup",a,s),window.addEventListener("pointercancel",l,s)};return i.forEach(t=>{(function(t){return Q.has(t.tagName)||-1!==t.tabIndex})(t)||null!==t.getAttribute("tabindex")||(t.tabIndex=0);(n.useGlobalTarget?window:t).addEventListener("pointerdown",r,s),t.addEventListener("focus",t=>((t,e)=>{const n=t.currentTarget;if(!n)return;const i=et(()=>{if(tt.has(n))return;nt(n,"down");const t=et(()=>{nt(n,"up")});n.addEventListener("keyup",t,e),n.addEventListener("blur",()=>nt(n,"cancel"),e)});n.addEventListener("keydown",i,e),n.addEventListener("blur",()=>n.removeEventListener("keydown",i),e)})(t,s),s)}),o}const ot={skipAnimations:!1,useManualTiming:!1};const rt=["read","resolveKeyframes","update","preRender","render","postRender"];function at(t,e){let n=!1,i=!0;const s={delta:0,timestamp:0,isProcessing:!1},o=()=>n=!0,r=rt.reduce((t,e)=>(t[e]=function(t){let e=new Set,n=new Set,i=!1,s=!1;const o=new WeakSet;let r={delta:0,timestamp:0,isProcessing:!1};function a(e){o.has(e)&&(l.schedule(e),t()),e(r)}const l={schedule:(t,s=!1,r=!1)=>{const a=r&&i?e:n;return s&&o.add(t),a.has(t)||a.add(t),t},cancel:t=>{n.delete(t),o.delete(t)},process:t=>{r=t,i?s=!0:(i=!0,[e,n]=[n,e],e.forEach(a),e.clear(),i=!1,s&&(s=!1,l.process(t)))}};return l}(o),t),{}),{read:a,resolveKeyframes:l,update:u,preRender:c,render:h,postRender:d}=r,p=()=>{const o=ot.useManualTiming?s.timestamp:performance.now();n=!1,s.delta=i?1e3/60:Math.max(Math.min(o-s.timestamp,40),1),s.timestamp=o,s.isProcessing=!0,a.process(s),l.process(s),u.process(s),c.process(s),h.process(s),d.process(s),s.isProcessing=!1,n&&e&&(i=!1,t(p))};return{schedule:rt.reduce((e,o)=>{const a=r[o];return e[o]=(e,o=!1,r=!1)=>(n||(n=!0,i=!0,s.isProcessing||t(p)),a.schedule(e,o,r)),e},{}),cancel:t=>{for(let e=0;e<rt.length;e++)r[rt[e]].cancel(t)},state:s,steps:r}}const{schedule:lt,cancel:ut,state:ct,steps:ht}=at("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:b,!0),dt=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],pt=new Set(dt),mt=new Set(["width","height","top","left","right","bottom",...dt]);let ft;function gt(){ft=void 0}const yt={now:()=>(void 0===ft&&yt.set(ct.isProcessing||ot.useManualTiming?ct.timestamp:performance.now()),ft),set:t=>{ft=t,queueMicrotask(gt)}};function vt(t,e){-1===t.indexOf(e)&&t.push(e)}function xt(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}class wt{constructor(){this.subscriptions=[]}add(t){return vt(this.subscriptions,t),()=>xt(this.subscriptions,t)}notify(t,e,n){const i=this.subscriptions.length;if(i)if(1===i)this.subscriptions[0](t,e,n);else for(let s=0;s<i;s++){const i=this.subscriptions[s];i&&i(t,e,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function Pt(t,e){return e?t*(1e3/e):0}const Tt={current:void 0};class St{constructor(t,e={}){this.version="11.18.2",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{const n=yt.now();this.updatedAt!==n&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),e&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){var e;this.current=t,this.updatedAt=yt.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=(e=this.current,!isNaN(parseFloat(e))))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new wt);const n=this.events[t].add(e);return"change"===t?()=>{n(),lt.read(()=>{this.events.change.getSize()||this.stop()})}:n}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,n){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-n}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return Tt.current&&Tt.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){const t=yt.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;const e=Math.min(this.updatedAt-this.prevUpdatedAt,30);return Pt(parseFloat(this.current)-parseFloat(this.prevFrameValue),e)}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function bt(t,e){return new St(t,e)}function At(t,e,n){t.hasValue(e)?t.getValue(e).set(n):t.addValue(e,bt(n))}function Et(t,e){const n=P(t,e);let{transitionEnd:i={},transition:s={},...o}=n||{};o={...o,...i};for(const e in o){At(t,e,(r=o[e],g(r)?r[r.length-1]||0:r))}var r}const Mt=t=>Boolean(t&&t.getVelocity);function Ct(t,e){const n=t.getValue("willChange");if(i=n,Boolean(Mt(i)&&i.add))return n.add(e);var i}const Vt=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),Rt="data-"+Vt("framerAppearId");function Dt(t){return t.props[Rt]}const kt={current:!1},Lt=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t;function Bt(t,e,n,i){if(t===e&&n===i)return b;const s=e=>function(t,e,n,i,s){let o,r,a=0;do{r=e+(n-e)/2,o=Lt(r,i,s)-t,o>0?n=r:e=r}while(Math.abs(o)>1e-7&&++a<12);return r}(e,0,1,t,n);return t=>0===t||1===t?t:Lt(s(t),e,i)}const Ft=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,jt=t=>e=>1-t(1-e),Ot=Bt(.33,1.53,.69,.99),It=jt(Ot),Ut=Ft(It),Wt=t=>(t*=2)<1?.5*It(t):.5*(2-Math.pow(2,-10*(t-1))),Nt=t=>1-Math.sin(Math.acos(t)),zt=jt(Nt),$t=Ft(Nt),Ht=t=>/^0[^.\s]+$/u.test(t);const Yt=(t,e,n)=>n>e?e:n<t?t:n,Xt={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},Kt={...Xt,transform:t=>Yt(0,1,t)},Gt={...Xt,default:1},_t=t=>Math.round(1e5*t)/1e5,qt=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;const Zt=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,Jt=(t,e)=>n=>Boolean("string"==typeof n&&Zt.test(n)&&n.startsWith(t)||e&&!function(t){return null==t}(n)&&Object.prototype.hasOwnProperty.call(n,e)),Qt=(t,e,n)=>i=>{if("string"!=typeof i)return i;const[s,o,r,a]=i.match(qt);return{[t]:parseFloat(s),[e]:parseFloat(o),[n]:parseFloat(r),alpha:void 0!==a?parseFloat(a):1}},te={...Xt,transform:t=>Math.round((t=>Yt(0,255,t))(t))},ee={test:Jt("rgb","red"),parse:Qt("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:i=1})=>"rgba("+te.transform(t)+", "+te.transform(e)+", "+te.transform(n)+", "+_t(Kt.transform(i))+")"};const ne={test:Jt("#"),parse:function(t){let e="",n="",i="",s="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),i=t.substring(5,7),s=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),i=t.substring(3,4),s=t.substring(4,5),e+=e,n+=n,i+=i,s+=s),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(i,16),alpha:s?parseInt(s,16)/255:1}},transform:ee.transform},ie=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),se=ie("deg"),oe=ie("%"),re=ie("px"),ae=ie("vh"),le=ie("vw"),ue={...oe,parse:t=>oe.parse(t)/100,transform:t=>oe.transform(100*t)},ce={test:Jt("hsl","hue"),parse:Qt("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:i=1})=>"hsla("+Math.round(t)+", "+oe.transform(_t(e))+", "+oe.transform(_t(n))+", "+_t(Kt.transform(i))+")"},he={test:t=>ee.test(t)||ne.test(t)||ce.test(t),parse:t=>ee.test(t)?ee.parse(t):ce.test(t)?ce.parse(t):ne.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?ee.transform(t):ce.transform(t)},de=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;const pe=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function me(t){const e=t.toString(),n=[],i={color:[],number:[],var:[]},s=[];let o=0;const r=e.replace(pe,t=>(he.test(t)?(i.color.push(o),s.push("color"),n.push(he.parse(t))):t.startsWith("var(")?(i.var.push(o),s.push("var"),n.push(t)):(i.number.push(o),s.push("number"),n.push(parseFloat(t))),++o,"${}")).split("${}");return{values:n,split:r,indexes:i,types:s}}function fe(t){return me(t).values}function ge(t){const{split:e,types:n}=me(t),i=e.length;return t=>{let s="";for(let o=0;o<i;o++)if(s+=e[o],void 0!==t[o]){const e=n[o];s+="number"===e?_t(t[o]):"color"===e?he.transform(t[o]):t[o]}return s}}const ye=t=>"number"==typeof t?0:t;const ve={test:function(t){var e,n;return isNaN(t)&&"string"==typeof t&&((null===(e=t.match(qt))||void 0===e?void 0:e.length)||0)+((null===(n=t.match(de))||void 0===n?void 0:n.length)||0)>0},parse:fe,createTransformer:ge,getAnimatableNone:function(t){const e=fe(t);return ge(t)(e.map(ye))}},xe=new Set(["brightness","contrast","saturate","opacity"]);function we(t){const[e,n]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;const[i]=n.match(qt)||[];if(!i)return t;const s=n.replace(i,"");let o=xe.has(e)?1:0;return i!==n&&(o*=100),e+"("+o+s+")"}const Pe=/\b([a-z-]*)\(.*?\)/gu,Te={...ve,getAnimatableNone:t=>{const e=t.match(Pe);return e?e.map(we).join(" "):t}},Se={borderWidth:re,borderTopWidth:re,borderRightWidth:re,borderBottomWidth:re,borderLeftWidth:re,borderRadius:re,radius:re,borderTopLeftRadius:re,borderTopRightRadius:re,borderBottomRightRadius:re,borderBottomLeftRadius:re,width:re,maxWidth:re,height:re,maxHeight:re,top:re,right:re,bottom:re,left:re,padding:re,paddingTop:re,paddingRight:re,paddingBottom:re,paddingLeft:re,margin:re,marginTop:re,marginRight:re,marginBottom:re,marginLeft:re,backgroundPositionX:re,backgroundPositionY:re},be={rotate:se,rotateX:se,rotateY:se,rotateZ:se,scale:Gt,scaleX:Gt,scaleY:Gt,scaleZ:Gt,skew:se,skewX:se,skewY:se,distance:re,translateX:re,translateY:re,translateZ:re,x:re,y:re,z:re,perspective:re,transformPerspective:re,opacity:Kt,originX:ue,originY:ue,originZ:re},Ae={...Xt,transform:Math.round},Ee={...Se,...be,zIndex:Ae,size:re,fillOpacity:Kt,strokeOpacity:Kt,numOctaves:Ae},Me={...Ee,color:he,backgroundColor:he,outlineColor:he,fill:he,stroke:he,borderColor:he,borderTopColor:he,borderRightColor:he,borderBottomColor:he,borderLeftColor:he,filter:Te,WebkitFilter:Te},Ce=t=>Me[t];function Ve(t,e){let n=Ce(t);return n!==Te&&(n=ve),n.getAnimatableNone?n.getAnimatableNone(e):void 0}const Re=new Set(["auto","none","0"]);const De=t=>t===Xt||t===re,ke=(t,e)=>parseFloat(t.split(", ")[e]),Le=(t,e)=>(n,{transform:i})=>{if("none"===i||!i)return 0;const s=i.match(/^matrix3d\((.+)\)$/u);if(s)return ke(s[1],e);{const e=i.match(/^matrix\((.+)\)$/u);return e?ke(e[1],t):0}},Be=new Set(["x","y","z"]),Fe=dt.filter(t=>!Be.has(t));const je={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:Le(4,13),y:Le(5,14)};je.translateX=je.x,je.translateY=je.y;const Oe=new Set;let Ie=!1,Ue=!1;function We(){if(Ue){const t=Array.from(Oe).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),n=new Map;e.forEach(t=>{const e=function(t){const e=[];return Fe.forEach(n=>{const i=t.getValue(n);void 0!==i&&(e.push([n,i.get()]),i.set(n.startsWith("scale")?1:0))}),e}(t);e.length&&(n.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();const e=n.get(t);e&&e.forEach(([e,n])=>{var i;null===(i=t.getValue(e))||void 0===i||i.set(n)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}Ue=!1,Ie=!1,Oe.forEach(t=>t.complete()),Oe.clear()}function Ne(){Oe.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(Ue=!0)})}class ze{constructor(t,e,n,i,s,o=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=n,this.motionValue=i,this.element=s,this.isAsync=o}scheduleResolve(){this.isScheduled=!0,this.isAsync?(Oe.add(this),Ie||(Ie=!0,lt.read(Ne),lt.resolveKeyframes(We))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:e,element:n,motionValue:i}=this;for(let s=0;s<t.length;s++)if(null===t[s])if(0===s){const s=null==i?void 0:i.get(),o=t[t.length-1];if(void 0!==s)t[0]=s;else if(n&&e){const i=n.readValue(e,o);null!=i&&(t[0]=i)}void 0===t[0]&&(t[0]=o),i&&void 0===s&&i.set(t[0])}else t[s]=t[s-1]}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),Oe.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,Oe.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}const $e=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),He=t=>e=>"string"==typeof e&&e.startsWith(t),Ye=He("--"),Xe=He("var(--"),Ke=t=>!!Xe(t)&&Ge.test(t.split("/*")[0].trim()),Ge=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,_e=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function qe(t,e,n=1){const[i,s]=function(t){const e=_e.exec(t);if(!e)return[,];const[,n,i,s]=e;return["--"+(null!=n?n:i),s]}(t);if(!i)return;const o=window.getComputedStyle(e).getPropertyValue(i);if(o){const t=o.trim();return $e(t)?parseFloat(t):t}return Ke(s)?qe(s,e,n+1):s}const Ze=t=>e=>e.test(t),Je=[Xt,re,oe,se,le,ae,{test:t=>"auto"===t,parse:t=>t}],Qe=t=>Je.find(Ze(t));class tn extends ze{constructor(t,e,n,i,s){super(t,e,n,i,s,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:e,name:n}=this;if(!e||!e.current)return;super.readKeyframes();for(let n=0;n<t.length;n++){let i=t[n];if("string"==typeof i&&(i=i.trim(),Ke(i))){const s=qe(i,e.current);void 0!==s&&(t[n]=s),n===t.length-1&&(this.finalKeyframe=i)}}if(this.resolveNoneKeyframes(),!mt.has(n)||2!==t.length)return;const[i,s]=t,o=Qe(i),r=Qe(s);if(o!==r)if(De(o)&&De(r))for(let e=0;e<t.length;e++){const n=t[e];"string"==typeof n&&(t[e]=parseFloat(n))}else this.needsMeasurement=!0}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:e}=this,n=[];for(let e=0;e<t.length;e++)("number"==typeof(i=t[e])?0===i:null===i||"none"===i||"0"===i||Ht(i))&&n.push(e);var i;n.length&&function(t,e,n){let i=0,s=void 0;for(;i<t.length&&!s;){const e=t[i];"string"==typeof e&&!Re.has(e)&&me(e).values.length&&(s=t[i]),i++}if(s&&n)for(const i of e)t[i]=Ve(n,s)}(t,n,e)}measureInitialState(){const{element:t,unresolvedKeyframes:e,name:n}=this;if(!t||!t.current)return;"height"===n&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=je[n](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;const i=e[e.length-1];void 0!==i&&t.getValue(n,i).jump(i,!1)}measureEndState(){var t;const{element:e,name:n,unresolvedKeyframes:i}=this;if(!e||!e.current)return;const s=e.getValue(n);s&&s.jump(this.measuredOrigin,!1);const o=i.length-1,r=i[o];i[o]=je[n](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==r&&void 0===this.finalKeyframe&&(this.finalKeyframe=r),(null===(t=this.removedTransforms)||void 0===t?void 0:t.length)&&this.removedTransforms.forEach(([t,n])=>{e.getValue(t).set(n)}),this.resolveNoneKeyframes()}}const en=(t,e)=>"zIndex"!==e&&(!("number"!=typeof t&&!Array.isArray(t))||!("string"!=typeof t||!ve.test(t)&&"0"!==t||t.startsWith("url(")));function nn(t,e,n,i){const s=t[0];if(null===s)return!1;if("display"===e||"visibility"===e)return!0;const o=t[t.length-1],r=en(s,e),a=en(o,e);return!(!r||!a)&&(function(t){const e=t[0];if(1===t.length)return!0;for(let n=0;n<t.length;n++)if(t[n]!==e)return!0}(t)||("spring"===n||j(n))&&i)}const sn=t=>null!==t;function on(t,{repeat:e,repeatType:n="loop"},i){const s=t.filter(sn),o=e&&"loop"!==n&&e%2==1?0:s.length-1;return o&&void 0!==i?i:s[o]}class rn{constructor({autoplay:t=!0,delay:e=0,type:n="keyframes",repeat:i=0,repeatDelay:s=0,repeatType:o="loop",...r}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=yt.now(),this.options={autoplay:t,delay:e,type:n,repeat:i,repeatDelay:s,repeatType:o,...r},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt}get resolved(){return this._resolved||this.hasAttemptedResolve||(Ne(),We()),this._resolved}onKeyframesResolved(t,e){this.resolvedAt=yt.now(),this.hasAttemptedResolve=!0;const{name:n,type:i,velocity:s,delay:o,onComplete:r,onUpdate:a,isGenerator:l}=this.options;if(!l&&!nn(t,n,i,s)){if(kt.current||!o)return a&&a(on(t,this.options,e)),r&&r(),void this.resolveFinishedPromise();this.options.duration=0}const u=this.initPlayback(t,e);!1!==u&&(this._resolved={keyframes:t,finalKeyframe:e,...u},this.onPostResolved())}onPostResolved(){}then(t,e){return this.currentFinishedPromise.then(t,e)}flatten(){this.options.type="keyframes",this.options.ease="linear"}updateFinishedPromise(){this.currentFinishedPromise=new Promise(t=>{this.resolveFinishedPromise=t})}}const an=(t,e,n)=>t+(e-t)*n;function ln(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+6*(e-t)*n:n<.5?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function un(t,e){return n=>n>0?e:t}const cn=(t,e,n)=>{const i=t*t,s=n*(e*e-i)+i;return s<0?0:Math.sqrt(s)},hn=[ne,ee,ce];function dn(t){const e=(n=t,hn.find(t=>t.test(n)));var n;if(!Boolean(e))return!1;let i=e.parse(t);return e===ce&&(i=function({hue:t,saturation:e,lightness:n,alpha:i}){t/=360,n/=100;let s=0,o=0,r=0;if(e/=100){const i=n<.5?n*(1+e):n+e-n*e,a=2*n-i;s=ln(a,i,t+1/3),o=ln(a,i,t),r=ln(a,i,t-1/3)}else s=o=r=n;return{red:Math.round(255*s),green:Math.round(255*o),blue:Math.round(255*r),alpha:i}}(i)),i}const pn=(t,e)=>{const n=dn(t),i=dn(e);if(!n||!i)return un(t,e);const s={...n};return t=>(s.red=cn(n.red,i.red,t),s.green=cn(n.green,i.green,t),s.blue=cn(n.blue,i.blue,t),s.alpha=an(n.alpha,i.alpha,t),ee.transform(s))},mn=(t,e)=>n=>e(t(n)),fn=(...t)=>t.reduce(mn),gn=new Set(["none","hidden"]);function yn(t,e){return n=>an(t,e,n)}function vn(t){return"number"==typeof t?yn:"string"==typeof t?Ke(t)?un:he.test(t)?pn:Pn:Array.isArray(t)?xn:"object"==typeof t?he.test(t)?pn:wn:un}function xn(t,e){const n=[...t],i=n.length,s=t.map((t,n)=>vn(t)(t,e[n]));return t=>{for(let e=0;e<i;e++)n[e]=s[e](t);return n}}function wn(t,e){const n={...t,...e},i={};for(const s in n)void 0!==t[s]&&void 0!==e[s]&&(i[s]=vn(t[s])(t[s],e[s]));return t=>{for(const e in i)n[e]=i[e](t);return n}}const Pn=(t,e)=>{const n=ve.createTransformer(e),i=me(t),s=me(e);return i.indexes.var.length===s.indexes.var.length&&i.indexes.color.length===s.indexes.color.length&&i.indexes.number.length>=s.indexes.number.length?gn.has(t)&&!s.values.length||gn.has(e)&&!i.values.length?function(t,e){return gn.has(t)?n=>n<=0?t:e:n=>n>=1?e:t}(t,e):fn(xn(function(t,e){var n;const i=[],s={color:0,var:0,number:0};for(let o=0;o<e.values.length;o++){const r=e.types[o],a=t.indexes[r][s[r]],l=null!==(n=t.values[a])&&void 0!==n?n:0;i[o]=l,s[r]++}return i}(i,s),s.values),n):un(t,e)};function Tn(t,e,n){if("number"==typeof t&&"number"==typeof e&&"number"==typeof n)return an(t,e,n);return vn(t)(t,e)}function Sn(t,e,n){const i=Math.max(e-5,0);return Pt(n-t(i),e-i)}const bn=100,An=10,En=1,Mn=0,Cn=800,Vn=.3,Rn=.3,Dn={granular:.01,default:2},kn={granular:.005,default:.5},Ln=.01,Bn=10,Fn=.05,jn=1;function On({duration:t=Cn,bounce:e=Vn,velocity:n=Mn,mass:i=En}){let s,o,r=1-e;r=Yt(Fn,jn,r),t=Yt(Ln,Bn,R(t)),r<1?(s=e=>{const i=e*r,s=i*t;return.001-(i-n)/In(e,r)*Math.exp(-s)},o=e=>{const i=e*r*t,o=i*n+n,a=Math.pow(r,2)*Math.pow(e,2)*t,l=Math.exp(-i),u=In(Math.pow(e,2),r);return(.001-s(e)>0?-1:1)*((o-a)*l)/u}):(s=e=>Math.exp(-e*t)*((e-n)*t+1)-.001,o=e=>Math.exp(-e*t)*(t*t*(n-e)));const a=function(t,e,n){let i=n;for(let n=1;n<12;n++)i-=t(i)/e(i);return i}(s,o,5/t);if(t=V(t),isNaN(a))return{stiffness:bn,damping:An,duration:t};{const e=Math.pow(a,2)*i;return{stiffness:e,damping:2*r*Math.sqrt(i*e),duration:t}}}function In(t,e){return t*Math.sqrt(1-e*e)}const Un=["duration","bounce"],Wn=["stiffness","damping","mass"];function Nn(t,e){return e.some(e=>void 0!==t[e])}function zn(t=Rn,e=Vn){const n="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t;let{restSpeed:i,restDelta:s}=n;const o=n.keyframes[0],r=n.keyframes[n.keyframes.length-1],a={done:!1,value:o},{stiffness:l,damping:u,mass:c,duration:h,velocity:d,isResolvedFromDuration:p}=function(t){let e={velocity:Mn,stiffness:bn,damping:An,mass:En,isResolvedFromDuration:!1,...t};if(!Nn(t,Wn)&&Nn(t,Un))if(t.visualDuration){const n=t.visualDuration,i=2*Math.PI/(1.2*n),s=i*i,o=2*Yt(.05,1,1-(t.bounce||0))*Math.sqrt(s);e={...e,mass:En,stiffness:s,damping:o}}else{const n=On(t);e={...e,...n,mass:En},e.isResolvedFromDuration=!0}return e}({...n,velocity:-R(n.velocity||0)}),m=d||0,f=u/(2*Math.sqrt(l*c)),g=r-o,y=R(Math.sqrt(l/c)),v=Math.abs(g)<5;let x;if(i||(i=v?Dn.granular:Dn.default),s||(s=v?kn.granular:kn.default),f<1){const t=In(y,f);x=e=>{const n=Math.exp(-f*y*e);return r-n*((m+f*y*g)/t*Math.sin(t*e)+g*Math.cos(t*e))}}else if(1===f)x=t=>r-Math.exp(-y*t)*(g+(m+y*g)*t);else{const t=y*Math.sqrt(f*f-1);x=e=>{const n=Math.exp(-f*y*e),i=Math.min(t*e,300);return r-n*((m+f*y*g)*Math.sinh(i)+t*g*Math.cosh(i))/t}}const w={calculatedDuration:p&&h||null,next:t=>{const e=x(t);if(p)a.done=t>=h;else{let n=0;f<1&&(n=0===t?V(m):Sn(x,t,e));const o=Math.abs(n)<=i,l=Math.abs(r-e)<=s;a.done=o&&l}return a.value=a.done?r:e,a},toString:()=>{const t=Math.min(B(w),2e4),e=z(e=>w.next(t*e).value,t,30);return t+"ms "+e}};return w}function $n({keyframes:t,velocity:e=0,power:n=.8,timeConstant:i=325,bounceDamping:s=10,bounceStiffness:o=500,modifyTarget:r,min:a,max:l,restDelta:u=.5,restSpeed:c}){const h=t[0],d={done:!1,value:h},p=t=>void 0===a?l:void 0===l||Math.abs(a-t)<Math.abs(l-t)?a:l;let m=n*e;const f=h+m,g=void 0===r?f:r(f);g!==f&&(m=g-h);const y=t=>-m*Math.exp(-t/i),v=t=>g+y(t),x=t=>{const e=y(t),n=v(t);d.done=Math.abs(e)<=u,d.value=d.done?g:n};let w,P;const T=t=>{var e;(e=d.value,void 0!==a&&e<a||void 0!==l&&e>l)&&(w=t,P=zn({keyframes:[d.value,p(d.value)],velocity:Sn(v,t,d.value),damping:s,stiffness:o,restDelta:u,restSpeed:c}))};return T(0),{calculatedDuration:null,next:t=>{let e=!1;return P||void 0!==w||(e=!0,x(t),T(t)),void 0!==w&&t>=w?P.next(t-w):(!e&&x(t),d)}}}const Hn=Bt(.42,0,1,1),Yn=Bt(0,0,.58,1),Xn=Bt(.42,0,.58,1),Kn=t=>Array.isArray(t)&&"number"!=typeof t[0],Gn={linear:b,easeIn:Hn,easeInOut:Xn,easeOut:Yn,circIn:Nt,circInOut:$t,circOut:zt,backIn:It,backInOut:Ut,backOut:Ot,anticipate:Wt},_n=t=>{if(I(t)){E(4===t.length);const[e,n,i,s]=t;return Bt(e,n,i,s)}return"string"==typeof t?Gn[t]:t};function qn(t,e,{clamp:n=!0,ease:i,mixer:s}={}){const o=t.length;if(E(o===e.length),1===o)return()=>e[0];if(2===o&&e[0]===e[1])return()=>e[1];const r=t[0]===t[1];t[0]>t[o-1]&&(t=[...t].reverse(),e=[...e].reverse());const a=function(t,e,n){const i=[],s=n||Tn,o=t.length-1;for(let n=0;n<o;n++){let o=s(t[n],t[n+1]);if(e){const t=Array.isArray(e)?e[n]||b:e;o=fn(t,o)}i.push(o)}return i}(e,i,s),l=a.length,u=n=>{if(r&&n<t[0])return e[0];let i=0;if(l>1)for(;i<t.length-2&&!(n<t[i+1]);i++);const s=C(t[i],t[i+1],n);return a[i](s)};return n?e=>u(Yt(t[0],t[o-1],e)):u}function Zn(t,e){const n=t[t.length-1];for(let i=1;i<=e;i++){const s=C(0,e,i);t.push(an(n,1,s))}}function Jn(t){const e=[0];return Zn(e,t.length-1),e}function Qn({duration:t=300,keyframes:e,times:n,ease:i="easeInOut"}){const s=Kn(i)?i.map(_n):_n(i),o={done:!1,value:e[0]},r=qn(function(t,e){return t.map(t=>t*e)}(n&&n.length===e.length?n:Jn(e),t),e,{ease:Array.isArray(s)?s:(a=e,l=s,a.map(()=>l||Xn).splice(0,a.length-1))});var a,l;return{calculatedDuration:t,next:e=>(o.value=r(e),o.done=e>=t,o)}}const ti=t=>{const e=({timestamp:e})=>t(e);return{start:()=>lt.update(e,!0),stop:()=>ut(e),now:()=>ct.isProcessing?ct.timestamp:yt.now()}},ei={decay:$n,inertia:$n,tween:Qn,keyframes:Qn,spring:zn},ni=t=>t/100;class ii extends rn{constructor(t){super(t),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.teardown();const{onStop:t}=this.options;t&&t()};const{name:e,motionValue:n,element:i,keyframes:s}=this.options,o=(null==i?void 0:i.KeyframeResolver)||ze;this.resolver=new o(s,(t,e)=>this.onKeyframesResolved(t,e),e,n,i),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(t){const{type:e="keyframes",repeat:n=0,repeatDelay:i=0,repeatType:s,velocity:o=0}=this.options,r=j(e)?e:ei[e]||Qn;let a,l;r!==Qn&&"number"!=typeof t[0]&&(a=fn(ni,Tn(t[0],t[1])),t=[0,100]);const u=r({...this.options,keyframes:t});"mirror"===s&&(l=r({...this.options,keyframes:[...t].reverse(),velocity:-o})),null===u.calculatedDuration&&(u.calculatedDuration=B(u));const{calculatedDuration:c}=u,h=c+i;return{generator:u,mirroredGenerator:l,mapPercentToKeyframes:a,calculatedDuration:c,resolvedDuration:h,totalDuration:h*(n+1)-i}}onPostResolved(){const{autoplay:t=!0}=this.options;this.play(),"paused"!==this.pendingPlayState&&t?this.state=this.pendingPlayState:this.pause()}tick(t,e=!1){const{resolved:n}=this;if(!n){const{keyframes:t}=this.options;return{done:!0,value:t[t.length-1]}}const{finalKeyframe:i,generator:s,mirroredGenerator:o,mapPercentToKeyframes:r,keyframes:a,calculatedDuration:l,totalDuration:u,resolvedDuration:c}=n;if(null===this.startTime)return s.next(0);const{delay:h,repeat:d,repeatType:p,repeatDelay:m,onUpdate:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-u/this.speed,this.startTime)),e?this.currentTime=t:null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=Math.round(t-this.startTime)*this.speed;const g=this.currentTime-h*(this.speed>=0?1:-1),y=this.speed>=0?g<0:g>u;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=u);let v=this.currentTime,x=s;if(d){const t=Math.min(this.currentTime,u)/c;let e=Math.floor(t),n=t%1;!n&&t>=1&&(n=1),1===n&&e--,e=Math.min(e,d+1);Boolean(e%2)&&("reverse"===p?(n=1-n,m&&(n-=m/c)):"mirror"===p&&(x=o)),v=Yt(0,1,n)*c}const w=y?{done:!1,value:a[0]}:x.next(v);r&&(w.value=r(w.value));let{done:P}=w;y||null===l||(P=this.speed>=0?this.currentTime>=u:this.currentTime<=0);const T=null===this.holdTime&&("finished"===this.state||"running"===this.state&&P);return T&&void 0!==i&&(w.value=on(a,this.options,i)),f&&f(w.value),T&&this.finish(),w}get duration(){const{resolved:t}=this;return t?R(t.calculatedDuration):0}get time(){return R(this.currentTime)}set time(t){t=V(t),this.currentTime=t,null!==this.holdTime||0===this.speed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.speed)}get speed(){return this.playbackSpeed}set speed(t){const e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=R(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved)return void(this.pendingPlayState="running");if(this.isStopped)return;const{driver:t=ti,onPlay:e,startTime:n}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),e&&e();const i=this.driver.now();null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime?"finished"===this.state&&(this.startTime=i):this.startTime=null!=n?n:this.calcStartTime(),"finished"===this.state&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var t;this._resolved?(this.state="paused",this.holdTime=null!==(t=this.currentTime)&&void 0!==t?t:0):this.pendingPlayState="paused"}complete(){"running"!==this.state&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";const{onComplete:t}=this.options;t&&t()}cancel(){null!==this.cancelTime&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}}function si(t){return new ii(t)}const oi=new Set(["opacity","clipPath","filter","transform"]);function ri(t,e,n,{delay:i=0,duration:s=300,repeat:o=0,repeatType:r="loop",ease:a="easeInOut",times:l}={}){const u={[e]:n};l&&(u.offset=l);const c=function t(e,n){return e?"function"==typeof e&&N()?z(e,n):I(e)?H(e):Array.isArray(e)?e.map(e=>t(e,n)||Y.easeOut):Y[e]:void 0}(a,s);return Array.isArray(c)&&(u.easing=c),t.animate(u,{delay:i,duration:s,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:o+1,direction:"reverse"===r?"alternate":"normal"})}const ai=M(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));const li={anticipate:Wt,backInOut:Ut,circInOut:$t};class ui extends rn{constructor(t){super(t);const{name:e,motionValue:n,element:i,keyframes:s}=this.options;this.resolver=new tn(s,(t,e)=>this.onKeyframesResolved(t,e),e,n,i),this.resolver.scheduleResolve()}initPlayback(t,e){let{duration:n=300,times:i,ease:s,type:o,motionValue:r,name:a,startTime:l}=this.options;if(!r.owner||!r.owner.current)return!1;var u;if("string"==typeof s&&N()&&s in li&&(s=li[s]),j((u=this.options).type)||"spring"===u.type||!$(u.ease)){const{onComplete:e,onUpdate:r,motionValue:a,element:l,...u}=this.options,c=function(t,e){const n=new ii({...e,keyframes:t,repeat:0,delay:0,isGenerator:!0});let i={done:!1,value:t[0]};const s=[];let o=0;for(;!i.done&&o<2e4;)i=n.sample(o),s.push(i.value),o+=10;return{times:void 0,keyframes:s,duration:o-10,ease:"linear"}}(t,u);1===(t=c.keyframes).length&&(t[1]=t[0]),n=c.duration,i=c.times,s=c.ease,o="keyframes"}const c=ri(r.owner.current,a,t,{...this.options,duration:n,times:i,ease:s});return c.startTime=null!=l?l:this.calcStartTime(),this.pendingTimeline?(O(c,this.pendingTimeline),this.pendingTimeline=void 0):c.onfinish=()=>{const{onComplete:n}=this.options;r.set(on(t,this.options,e)),n&&n(),this.cancel(),this.resolveFinishedPromise()},{animation:c,duration:n,times:i,type:o,ease:s,keyframes:t}}get duration(){const{resolved:t}=this;if(!t)return 0;const{duration:e}=t;return R(e)}get time(){const{resolved:t}=this;if(!t)return 0;const{animation:e}=t;return R(e.currentTime||0)}set time(t){const{resolved:e}=this;if(!e)return;const{animation:n}=e;n.currentTime=V(t)}get speed(){const{resolved:t}=this;if(!t)return 1;const{animation:e}=t;return e.playbackRate}set speed(t){const{resolved:e}=this;if(!e)return;const{animation:n}=e;n.playbackRate=t}get state(){const{resolved:t}=this;if(!t)return"idle";const{animation:e}=t;return e.playState}get startTime(){const{resolved:t}=this;if(!t)return null;const{animation:e}=t;return e.startTime}attachTimeline(t){if(this._resolved){const{resolved:e}=this;if(!e)return b;const{animation:n}=e;O(n,t)}else this.pendingTimeline=t;return b}play(){if(this.isStopped)return;const{resolved:t}=this;if(!t)return;const{animation:e}=t;"finished"===e.playState&&this.updateFinishedPromise(),e.play()}pause(){const{resolved:t}=this;if(!t)return;const{animation:e}=t;e.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.resolveFinishedPromise(),this.updateFinishedPromise();const{resolved:t}=this;if(!t)return;const{animation:e,keyframes:n,duration:i,type:s,ease:o,times:r}=t;if("idle"===e.playState||"finished"===e.playState)return;if(this.time){const{motionValue:t,onUpdate:e,onComplete:a,element:l,...u}=this.options,c=new ii({...u,keyframes:n,duration:i,type:s,ease:o,times:r,isGenerator:!0}),h=V(this.time);t.setWithVelocity(c.sample(h-10).value,c.sample(h).value,10)}const{onStop:a}=this.options;a&&a(),this.cancel()}complete(){const{resolved:t}=this;t&&t.animation.finish()}cancel(){const{resolved:t}=this;t&&t.animation.cancel()}static supports(t){const{motionValue:e,name:n,repeatDelay:i,repeatType:s,damping:o,type:r}=t;if(!(e&&e.owner&&e.owner.current instanceof HTMLElement))return!1;const{onUpdate:a,transformTemplate:l}=e.owner.getProps();return ai()&&n&&oi.has(n)&&!a&&!l&&!i&&"mirror"!==s&&0!==o&&"inertia"!==r}}const ci={type:"spring",stiffness:500,damping:25,restSpeed:10},hi={type:"keyframes",duration:.8},di={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},pi=(t,{keyframes:e})=>e.length>2?hi:pt.has(t)?t.startsWith("scale")?{type:"spring",stiffness:550,damping:0===e[1]?2*Math.sqrt(550):30,restSpeed:10}:ci:di;const mi=(t,e,n,i={},s,o)=>r=>{const a=L(i,t)||{},l=a.delay||i.delay||0;let{elapsed:u=0}=i;u-=V(l);let c={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-u,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{r(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:o?void 0:s};(function({when:t,delay:e,delayChildren:n,staggerChildren:i,staggerDirection:s,repeat:o,repeatType:r,repeatDelay:a,from:l,elapsed:u,...c}){return!!Object.keys(c).length})(a)||(c={...c,...pi(t,c)}),c.duration&&(c.duration=V(c.duration)),c.repeatDelay&&(c.repeatDelay=V(c.repeatDelay)),void 0!==c.from&&(c.keyframes[0]=c.from);let h=!1;if((!1===c.type||0===c.duration&&!c.repeatDelay)&&(c.duration=0,0===c.delay&&(h=!0)),(kt.current||ot.skipAnimations)&&(h=!0,c.duration=0,c.delay=0),h&&!o&&void 0!==e.get()){const t=on(c.keyframes,a);if(void 0!==t)return lt.update(()=>{c.onUpdate(t),c.onComplete()}),new k([])}return!o&&ui.supports(c)?new ui(c):new ii(c)};function fi({protectedKeys:t,needsAnimating:e},n){const i=t.hasOwnProperty(n)&&!0!==e[n];return e[n]=!1,i}function gi(t,e,{delay:n=0,transitionOverride:i,type:s}={}){var o;let{transition:r=t.getDefaultTransition(),transitionEnd:a,...l}=e;i&&(r=i);const u=[],c=s&&t.animationState&&t.animationState.getState()[s];for(const e in l){const i=t.getValue(e,null!==(o=t.latestValues[e])&&void 0!==o?o:null),s=l[e];if(void 0===s||c&&fi(c,e))continue;const a={delay:n,...L(r||{},e)};let h=!1;if(window.MotionHandoffAnimation){const n=Dt(t);if(n){const t=window.MotionHandoffAnimation(n,e,lt);null!==t&&(a.startTime=t,h=!0)}}Ct(t,e),i.start(mi(e,i,s,t.shouldReduceMotion&&mt.has(e)?{type:!1}:a,t,h));const d=i.animation;d&&u.push(d)}return a&&Promise.all(u).then(()=>{lt.update(()=>{a&&Et(t,a)})}),u}function yi(t,e,n={}){var i;const s=P(t,e,"exit"===n.type?null===(i=t.presenceContext)||void 0===i?void 0:i.custom:void 0);let{transition:o=t.getDefaultTransition()||{}}=s||{};n.transitionOverride&&(o=n.transitionOverride);const r=s?()=>Promise.all(gi(t,s,n)):()=>Promise.resolve(),a=t.variantChildren&&t.variantChildren.size?(i=0)=>{const{delayChildren:s=0,staggerChildren:r,staggerDirection:a}=o;return function(t,e,n=0,i=0,s=1,o){const r=[],a=(t.variantChildren.size-1)*i,l=1===s?(t=0)=>t*i:(t=0)=>a-t*i;return Array.from(t.variantChildren).sort(vi).forEach((t,i)=>{t.notify("AnimationStart",e),r.push(yi(t,e,{...o,delay:n+l(i)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(r)}(t,e,s+i,r,a,n)}:()=>Promise.resolve(),{when:l}=o;if(l){const[t,e]="beforeChildren"===l?[r,a]:[a,r];return t().then(()=>e())}return Promise.all([r(),a(n.delay)])}function vi(t,e){return t.sortNodePosition(e)}function xi(t,e,n={}){let i;if(t.notify("AnimationStart",e),Array.isArray(e)){const s=e.map(e=>yi(t,e,n));i=Promise.all(s)}else if("string"==typeof e)i=yi(t,e,n);else{const s="function"==typeof e?P(t,e,n.custom):e;i=Promise.all(gi(t,s,n))}return i.then(()=>{t.notify("AnimationComplete",e)})}const wi=S.length;const Pi=[...T].reverse(),Ti=T.length;function Si(t){let e=function(t){return e=>Promise.all(e.map(({animation:e,options:n})=>xi(t,e,n)))}(t),n=Ei(),i=!0;const s=e=>(n,i)=>{var s;const o=P(t,i,"exit"===e?null===(s=t.presenceContext)||void 0===s?void 0:s.custom:void 0);if(o){const{transition:t,transitionEnd:e,...i}=o;n={...n,...i,...e}}return n};function o(o){const{props:r}=t,a=function t(e){if(!e)return;if(!e.isControllingVariants){const n=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(n.initial=e.props.initial),n}const n={};for(let t=0;t<wi;t++){const i=S[t],s=e.props[i];(v(s)||!1===s)&&(n[i]=s)}return n}(t.parent)||{},l=[],u=new Set;let c={},h=1/0;for(let e=0;e<Ti;e++){const d=Pi[e],p=n[d],m=void 0!==r[d]?r[d]:a[d],x=v(m),w=d===o?p.isActive:null;!1===w&&(h=e);let P=m===a[d]&&m!==r[d]&&x;if(P&&i&&t.manuallyAnimateOnMount&&(P=!1),p.protectedKeys={...c},!p.isActive&&null===w||!m&&!p.prevProp||f(m)||"boolean"==typeof m)continue;const T=bi(p.prevProp,m);let S=T||d===o&&p.isActive&&!P&&x||e>h&&x,b=!1;const A=Array.isArray(m)?m:[m];let E=A.reduce(s(d),{});!1===w&&(E={});const{prevResolvedValues:M={}}=p,C={...M,...E},V=e=>{S=!0,u.has(e)&&(b=!0,u.delete(e)),p.needsAnimating[e]=!0;const n=t.getValue(e);n&&(n.liveStyle=!1)};for(const t in C){const e=E[t],n=M[t];if(c.hasOwnProperty(t))continue;let i=!1;i=g(e)&&g(n)?!y(e,n):e!==n,i?null!=e?V(t):u.add(t):void 0!==e&&u.has(t)?V(t):p.protectedKeys[t]=!0}p.prevProp=m,p.prevResolvedValues=E,p.isActive&&(c={...c,...E}),i&&t.blockInitialAnimation&&(S=!1);const R=!(P&&T)||b;S&&R&&l.push(...A.map(t=>({animation:t,options:{type:d}})))}if(u.size){const e={};u.forEach(n=>{const i=t.getBaseTarget(n),s=t.getValue(n);s&&(s.liveStyle=!0),e[n]=null!=i?i:null}),l.push({animation:e})}let d=Boolean(l.length);return!i||!1!==r.initial&&r.initial!==r.animate||t.manuallyAnimateOnMount||(d=!1),i=!1,d?e(l):Promise.resolve()}return{animateChanges:o,setActive:function(e,i){var s;if(n[e].isActive===i)return Promise.resolve();null===(s=t.variantChildren)||void 0===s||s.forEach(t=>{var n;return null===(n=t.animationState)||void 0===n?void 0:n.setActive(e,i)}),n[e].isActive=i;const r=o(e);for(const t in n)n[t].protectedKeys={};return r},setAnimateFunction:function(n){e=n(t)},getState:()=>n,reset:()=>{n=Ei(),i=!0}}}function bi(t,e){return"string"==typeof e?e!==t:!!Array.isArray(e)&&!y(e,t)}function Ai(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Ei(){return{animate:Ai(!0),whileInView:Ai(),whileHover:Ai(),whileTap:Ai(),whileDrag:Ai(),whileFocus:Ai(),exit:Ai()}}class Mi{constructor(t){this.isMounted=!1,this.node=t}update(){}}let Ci=0;const Vi={animation:{Feature:class extends Mi{constructor(t){super(t),t.animationState||(t.animationState=Si(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();f(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){var t;this.node.animationState.reset(),null===(t=this.unmountControls)||void 0===t||t.call(this)}}},exit:{Feature:class extends Mi{constructor(){super(...arguments),this.id=Ci++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===n)return;const i=this.node.animationState.setActive("exit",!t);e&&!t&&i.then(()=>e(this.id))}mount(){const{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}}},Ri=e.createContext({}),Di=()=>({x:{min:0,max:0},y:{min:0,max:0}});function ki(t){return t.max-t.min}function Li(t,e,n,i=.5){t.origin=i,t.originPoint=an(e.min,e.max,t.origin),t.scale=ki(n)/ki(e),t.translate=an(n.min,n.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function Bi(t,e,n,i){Li(t.x,e.x,n.x,i?i.originX:void 0),Li(t.y,e.y,n.y,i?i.originY:void 0)}function Fi(t,e,n){t.min=n.min+e.min,t.max=t.min+ki(e)}function ji(t,e,n){t.min=e.min-n.min,t.max=t.min+ki(e)}function Oi(t,e,n){ji(t.x,e.x,n.x),ji(t.y,e.y,n.y)}const Ii=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Ui(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||Ii.has(t)}let Wi=t=>!Ui(t);function Ni(t){t&&(Wi=e=>e.startsWith("on")?!Ui(e):t(e))}try{Ni(require("@emotion/is-prop-valid").default)}catch(t){}function zi(t,e,n){const i={};for(const s in t)"values"===s&&"object"==typeof t.values||(Wi(s)||!0===n&&Ui(s)||!e&&!Ui(s)||t.draggable&&s.startsWith("onDrag"))&&(i[s]=t[s]);return i}const $i=e.createContext(null);function Hi(t){return f(t.animate)||S.some(e=>v(t[e]))}function Yi(t){return Boolean(Hi(t)||t.variants)}function Xi(t){const n=e.useRef(null);return null===n.current&&(n.current=t()),n.current}function Ki(t){const e=Mt(t)?t.get():t;return n=e,Boolean(n&&"object"==typeof n&&n.mix&&n.toValue)?e.toValue():e;var n}const Gi=t=>(n,i)=>{const s=e.useContext(Ri),o=e.useContext($i),r=()=>function({scrapeMotionValuesFromProps:t,createRenderState:e,onUpdate:n},i,s,o){const r={latestValues:_i(i,s,o,t),renderState:e()};return n&&(r.onMount=t=>n({props:i,current:t,...r}),r.onUpdate=t=>n(t)),r}(t,n,s,o);return i?r():Xi(r)};function _i(t,e,n,i){const s={},o=i(t,{});for(const t in o)s[t]=Ki(o[t]);let{initial:r,animate:a}=t;const l=Hi(t),u=Yi(t);e&&u&&!l&&!1!==t.inherit&&(void 0===r&&(r=e.initial),void 0===a&&(a=e.animate));let c=!!n&&!1===n.initial;c=c||!1===r;const h=c?a:r;if(h&&"boolean"!=typeof h&&!f(h)){const e=Array.isArray(h)?h:[h];for(let n=0;n<e.length;n++){const i=w(t,e[n]);if(i){const{transitionEnd:t,transition:e,...n}=i;for(const t in n){let e=n[t];if(Array.isArray(e)){e=e[c?e.length-1:0]}null!==e&&(s[t]=e)}for(const e in t)s[e]=t[e]}}}return s}function qi(t,e,n,i={passive:!0}){return t.addEventListener(e,n,i),()=>t.removeEventListener(e,n)}function Zi(t){return{point:{x:t.pageX,y:t.pageY}}}const Ji=t=>e=>J(e)&&t(e,Zi(e));function Qi(t,e,n,i){return qi(t,e,Ji(n),i)}const ts="undefined"!=typeof window;function es(t){return e.useEffect(()=>()=>t(),[])}const ns=ts?e.useLayoutEffect:e.useEffect;function is(){const t=function(){const t=e.useRef(!1);return ns(()=>(t.current=!0,()=>{t.current=!1}),[]),t}(),[n,i]=e.useState(0),s=e.useCallback(()=>{t.current&&i(n+1)},[n]);return[e.useCallback(()=>lt.postRender(s),[s]),n]}const ss=e.createContext({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"});class os extends i.Component{getSnapshotBeforeUpdate(t){const e=this.props.childRef.current;if(e&&t.isPresent&&!this.props.isPresent){const t=this.props.sizeRef.current;t.height=e.offsetHeight||0,t.width=e.offsetWidth||0,t.top=e.offsetTop,t.left=e.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function rs({children:t,isPresent:n}){const s=e.useId(),o=e.useRef(null),r=e.useRef({width:0,height:0,top:0,left:0}),{nonce:a}=e.useContext(ss);return e.useInsertionEffect(()=>{const{width:t,height:e,top:i,left:l}=r.current;if(n||!o.current||!t||!e)return;o.current.dataset.motionPopId=s;const u=document.createElement("style");return a&&(u.nonce=a),document.head.appendChild(u),u.sheet&&u.sheet.insertRule(`\n          [data-motion-pop-id="${s}"] {\n            position: absolute !important;\n            width: ${t}px !important;\n            height: ${e}px !important;\n            top: ${i}px !important;\n            left: ${l}px !important;\n          }\n        `),()=>{document.head.removeChild(u)}},[n]),d(os,{isPresent:n,childRef:o,sizeRef:r,children:i.cloneElement(t,{ref:o})})}const as=({children:t,initial:n,isPresent:s,onExitComplete:o,custom:r,presenceAffectsLayout:a,mode:l})=>{const u=Xi(ls),c=e.useId(),h=e.useCallback(t=>{u.set(t,!0);for(const t of u.values())if(!t)return;o&&o()},[u,o]),p=e.useMemo(()=>({id:c,initial:n,isPresent:s,custom:r,onExitComplete:h,register:t=>(u.set(t,!1),()=>u.delete(t))}),a?[Math.random(),h]:[s,h]);return e.useMemo(()=>{u.forEach((t,e)=>u.set(e,!1))},[s]),i.useEffect(()=>{!s&&!u.size&&o&&o()},[s]),"popLayout"===l&&(t=d(rs,{isPresent:s,children:t})),d($i.Provider,{value:p,children:t})};function ls(){return new Map}function us(t=!0){const n=e.useContext($i);if(null===n)return[!0,null];const{isPresent:i,onExitComplete:s,register:o}=n,r=e.useId();e.useEffect(()=>{t&&o(r)},[t]);const a=e.useCallback(()=>t&&s&&s(r),[r,s,t]);return!i&&s?[!1,a]:[!0]}const cs=t=>t.key||"";function hs(t){const n=[];return e.Children.forEach(t,t=>{e.isValidElement(t)&&n.push(t)}),n}const ds=e.createContext(null);function ps(t,e,n){const i=Mt(t)?t:bt(t);return i.start(mi("",i,e,n)),i.animation}const ms=(t,e,n)=>{const i=e-t;return((n-t)%i+i)%i+t};function fs(t,e){return Kn(t)?t[ms(0,t.length,e)]:t}function gs(t){return"object"==typeof t&&!Array.isArray(t)}function ys(t,e,n,i){return"string"==typeof t&&gs(e)?G(t,n,i):t instanceof NodeList?Array.from(t):Array.isArray(t)?t:[t]}function vs(t,e,n){return t*(e+1)}function xs(t,e,n,i){var s;return"number"==typeof e?e:e.startsWith("-")||e.startsWith("+")?Math.max(0,t+parseFloat(e)):"<"===e?n:null!==(s=i.get(e))&&void 0!==s?s:t}function ws(t,e,n,i,s,o){!function(t,e,n){for(let i=0;i<t.length;i++){const s=t[i];s.at>e&&s.at<n&&(xt(t,s),i--)}}(t,s,o);for(let r=0;r<e.length;r++)t.push({value:e[r],at:an(s,o,i[r]),easing:fs(n,r)})}function Ps(t,e){for(let n=0;n<t.length;n++)t[n]=t[n]/(e+1)}function Ts(t,e){return t.at===e.at?null===t.value?1:null===e.value?-1:0:t.at-e.at}function Ss(t,e){return!e.has(t)&&e.set(t,{}),e.get(t)}function bs(t,e){return e[t]||(e[t]=[]),e[t]}function As(t){return Array.isArray(t)?t:[t]}function Es(t,e){return t&&t[e]?{...t,...t[e]}:{...t}}const Ms=t=>"number"==typeof t,Cs=t=>t.every(Ms),Vs=new WeakMap;function Rs(t){return t instanceof SVGElement&&"svg"!==t.tagName}const Ds={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},ks={};for(const t in Ds)ks[t]={isEnabled:e=>Ds[t].some(t=>!!e[t])};const Ls={current:null},Bs={current:!1};function Fs(){if(Bs.current=!0,ts)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>Ls.current=t.matches;t.addListener(e),e()}else Ls.current=!1}const js=[...Je,he,ve];const Os=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class Is{scrapeMotionValuesFromProps(t,e,n){return{}}constructor({parent:t,props:e,presenceContext:n,reducedMotionConfig:i,blockInitialAnimation:s,visualState:o},r={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=ze,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const t=yt.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,lt.render(this.render,!1,!0))};const{latestValues:a,renderState:l,onUpdate:u}=o;this.onUpdate=u,this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=n,this.depth=t?t.depth+1:0,this.reducedMotionConfig=i,this.options=r,this.blockInitialAnimation=Boolean(s),this.isControllingVariants=Hi(e),this.isVariantNode=Yi(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=Boolean(t&&t.current);const{willChange:c,...h}=this.scrapeMotionValuesFromProps(e,{},this);for(const t in h){const e=h[t];void 0!==a[t]&&Mt(e)&&e.set(a[t],!1)}}mount(t){this.current=t,Vs.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),Bs.current||Fs(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||Ls.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){Vs.delete(this.current),this.projection&&this.projection.unmount(),ut(this.notifyUpdate),ut(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();const n=pt.has(t),i=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&lt.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)}),s=e.on("renderRequest",this.scheduleRender);let o;window.MotionCheckAppearSync&&(o=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{i(),s(),o&&o(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in ks){const e=ks[t];if(!e)continue;const{isEnabled:n,Feature:i}=e;if(!this.features[t]&&i&&n(this.props)&&(this.features[t]=new i(this)),this.features[t]){const e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):{x:{min:0,max:0},y:{min:0,max:0}}}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<Os.length;e++){const n=Os[e];this.propEventSubscriptions[n]&&(this.propEventSubscriptions[n](),delete this.propEventSubscriptions[n]);const i=t["on"+n];i&&(this.propEventSubscriptions[n]=this.on(n,i))}this.prevMotionValues=function(t,e,n){for(const i in e){const s=e[i],o=n[i];if(Mt(s))t.addValue(i,s);else if(Mt(o))t.addValue(i,bt(s,{owner:t}));else if(o!==s)if(t.hasValue(i)){const e=t.getValue(i);!0===e.liveStyle?e.jump(s):e.hasAnimated||e.set(s)}else{const e=t.getStaticValue(i);t.addValue(i,bt(void 0!==e?e:s,{owner:t}))}}for(const i in n)void 0===e[i]&&t.removeValue(i);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue(),this.onUpdate&&this.onUpdate(this)}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){const e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){const n=this.values.get(t);e!==n&&(n&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);const e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let n=this.values.get(t);return void 0===n&&void 0!==e&&(n=bt(null===e?void 0:e,{owner:this}),this.addValue(t,n)),n}readValue(t,e){var n;let i=void 0===this.latestValues[t]&&this.current?null!==(n=this.getBaseTargetFromProps(this.props,t))&&void 0!==n?n:this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];var s;return null!=i&&("string"==typeof i&&($e(i)||Ht(i))?i=parseFloat(i):(s=i,!js.find(Ze(s))&&ve.test(e)&&(i=Ve(t,e))),this.setBaseTarget(t,Mt(i)?i.get():i)),Mt(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){var e;const{initial:n}=this.props;let i;if("string"==typeof n||"object"==typeof n){const s=w(this.props,n,null===(e=this.presenceContext)||void 0===e?void 0:e.custom);s&&(i=s[t])}if(n&&void 0!==i)return i;const s=this.getBaseTargetFromProps(this.props,t);return void 0===s||Mt(s)?void 0!==this.initialValues[t]&&void 0===i?void 0:this.baseTarget[t]:s}on(t,e){return this.events[t]||(this.events[t]=new wt),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class Us extends Is{constructor(){super(...arguments),this.KeyframeResolver=tn}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:n}){delete e[t],delete n[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;Mt(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=""+t)}))}}const Ws=(t,e)=>e&&"number"==typeof t?e.transform(t):t,Ns={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},zs=dt.length;function $s(t,e,n){let i="",s=!0;for(let o=0;o<zs;o++){const r=dt[o],a=t[r];if(void 0===a)continue;let l=!0;if(l="number"==typeof a?a===(r.startsWith("scale")?1:0):0===parseFloat(a),!l||n){const t=Ws(a,Ee[r]);if(!l){s=!1;i+=`${Ns[r]||r}(${t}) `}n&&(e[r]=t)}}return i=i.trim(),n?i=n(e,s?"":i):s&&(i="none"),i}function Hs(t,e,n){const{style:i,vars:s,transformOrigin:o}=t;let r=!1,a=!1;for(const t in e){const n=e[t];if(pt.has(t))r=!0;else if(Ye(t))s[t]=n;else{const e=Ws(n,Ee[t]);t.startsWith("origin")?(a=!0,o[t]=e):i[t]=e}}if(e.transform||(r||n?i.transform=$s(e,t.transform,n):i.transform&&(i.transform="none")),a){const{originX:t="50%",originY:e="50%",originZ:n=0}=o;i.transformOrigin=`${t} ${e} ${n}`}}const Ys={offset:"stroke-dashoffset",array:"stroke-dasharray"},Xs={offset:"strokeDashoffset",array:"strokeDasharray"};function Ks(t,e,n){return"string"==typeof t?t:re.transform(e+n*t)}function Gs(t,{attrX:e,attrY:n,attrScale:i,originX:s,originY:o,pathLength:r,pathSpacing:a=1,pathOffset:l=0,...u},c,h){if(Hs(t,u,h),c)return void(t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox));t.attrs=t.style,t.style={};const{attrs:d,style:p,dimensions:m}=t;d.transform&&(m&&(p.transform=d.transform),delete d.transform),m&&(void 0!==s||void 0!==o||p.transform)&&(p.transformOrigin=function(t,e,n){return`${Ks(e,t.x,t.width)} ${Ks(n,t.y,t.height)}`}(m,void 0!==s?s:.5,void 0!==o?o:.5)),void 0!==e&&(d.x=e),void 0!==n&&(d.y=n),void 0!==i&&(d.scale=i),void 0!==r&&function(t,e,n=1,i=0,s=!0){t.pathLength=1;const o=s?Ys:Xs;t[o.offset]=re.transform(-i);const r=re.transform(e),a=re.transform(n);t[o.array]=`${r} ${a}`}(d,r,a,l,!1)}const _s=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]),qs=t=>"string"==typeof t&&"svg"===t.toLowerCase();function Zs(t,{style:e,vars:n},i,s){Object.assign(t.style,e,s&&s.getProjectionStyles(i));for(const e in n)t.style.setProperty(e,n[e])}function Js(t,e,n,i){Zs(t,e,void 0,i);for(const n in e.attrs)t.setAttribute(_s.has(n)?n:Vt(n),e.attrs[n])}const Qs={};function to(t){Object.assign(Qs,t)}function eo(t,{layout:e,layoutId:n}){return pt.has(t)||t.startsWith("origin")||(e||void 0!==n)&&(!!Qs[t]||"opacity"===t)}function no(t,e,n){var i;const{style:s}=t,o={};for(const r in s)(Mt(s[r])||e.style&&Mt(e.style[r])||eo(r,t)||void 0!==(null===(i=null==n?void 0:n.getValue(r))||void 0===i?void 0:i.liveStyle))&&(o[r]=s[r]);return o}function io(t,e,n){const i=no(t,e,n);for(const n in t)if(Mt(t[n])||Mt(e[n])){i[-1!==dt.indexOf(n)?"attr"+n.charAt(0).toUpperCase()+n.substring(1):n]=t[n]}return i}class so extends Us{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=Di}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(pt.has(e)){const t=Ce(e);return t&&t.default||0}return e=_s.has(e)?e:Vt(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,n){return io(t,e,n)}build(t,e,n){Gs(t,e,this.isSVGTag,n.transformTemplate)}renderInstance(t,e,n,i){Js(t,e,0,i)}mount(t){this.isSVGTag=qs(t.tagName),super.mount(t)}}function oo({top:t,left:e,right:n,bottom:i}){return{x:{min:e,max:n},y:{min:t,max:i}}}function ro(t){return void 0===t||1===t}function ao({scale:t,scaleX:e,scaleY:n}){return!ro(t)||!ro(e)||!ro(n)}function lo(t){return ao(t)||uo(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function uo(t){return co(t.x)||co(t.y)}function co(t){return t&&"0%"!==t}function ho(t,e,n){return n+e*(t-n)}function po(t,e,n,i,s){return void 0!==s&&(t=ho(t,s,i)),ho(t,n,i)+e}function mo(t,e=0,n=1,i,s){t.min=po(t.min,e,n,i,s),t.max=po(t.max,e,n,i,s)}function fo(t,{x:e,y:n}){mo(t.x,e.translate,e.scale,e.originPoint),mo(t.y,n.translate,n.scale,n.originPoint)}function go(t,e){t.min=t.min+e,t.max=t.max+e}function yo(t,e,n,i,s=.5){mo(t,e,n,an(t.min,t.max,s),i)}function vo(t,e){yo(t.x,e.x,e.scaleX,e.scale,e.originX),yo(t.y,e.y,e.scaleY,e.scale,e.originY)}function xo(t,e){return oo(function(t,e){if(!e)return t;const n=e({x:t.left,y:t.top}),i=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:i.y,right:i.x}}(t.getBoundingClientRect(),e))}class wo extends Us{constructor(){super(...arguments),this.type="html",this.renderInstance=Zs}readValueFromInstance(t,e){if(pt.has(e)){const t=Ce(e);return t&&t.default||0}{const i=(n=t,window.getComputedStyle(n)),s=(Ye(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof s?s.trim():s}var n}measureInstanceViewportBox(t,{transformPagePoint:e}){return xo(t,e)}build(t,e,n){Hs(t,e,n.transformTemplate)}scrapeMotionValuesFromProps(t,e,n){return no(t,e,n)}}class Po extends Is{constructor(){super(...arguments),this.type="object"}readValueFromInstance(t,e){if(function(t,e){return t in e}(e,t)){const n=t[e];if("string"==typeof n||"number"==typeof n)return n}}getBaseTargetFromProps(){}removeValueFromRenderState(t,e){delete e.output[t]}measureInstanceViewportBox(){return{x:{min:0,max:0},y:{min:0,max:0}}}build(t,e){Object.assign(t.output,e)}renderInstance(t,{output:e}){Object.assign(t,e)}sortInstanceNodePosition(){return 0}}function To(t){const e={presenceContext:null,props:{},visualState:{renderState:{transform:{},transformOrigin:{},style:{},vars:{},attrs:{}},latestValues:{}}},n=Rs(t)?new so(e):new wo(e);n.mount(t),Vs.set(t,n)}function So(t){const e=new Po({presenceContext:null,props:{},visualState:{renderState:{output:{}},latestValues:{}}});e.mount(t),Vs.set(t,e)}function bo(t,e,n,i){const s=[];if(function(t,e){return Mt(t)||"number"==typeof t||"string"==typeof t&&!gs(e)}(t,e))s.push(ps(t,gs(e)&&e.default||e,n&&n.default||n));else{const o=ys(t,e,i),r=o.length;for(let t=0;t<r;t++){const i=o[t],a=i instanceof Element?To:So;Vs.has(i)||a(i);const l=Vs.get(i),u={...n};"delay"in u&&"function"==typeof u.delay&&(u.delay=u.delay(t,r)),s.push(...gi(l,{...e,transition:u},{}))}}return s}function Ao(t,e,n){const i=[];return function(t,{defaultTransition:e={},...n}={},i,s){const o=e.duration||.3,r=new Map,a=new Map,l={},u=new Map;let c=0,h=0,d=0;for(let n=0;n<t.length;n++){const r=t[n];if("string"==typeof r){u.set(r,h);continue}if(!Array.isArray(r)){u.set(r.name,xs(h,r.at,c,u));continue}let[p,m,f={}]=r;void 0!==f.at&&(h=xs(h,f.at,c,u));let g=0;const y=(t,n,i,r=0,a=0)=>{const l=As(t),{delay:u=0,times:c=Jn(l),type:p="keyframes",repeat:m,repeatType:f,repeatDelay:y=0,...v}=n;let{ease:x=e.ease||"easeOut",duration:w}=n;const P="function"==typeof u?u(r,a):u,T=l.length,S=j(p)?p:null==s?void 0:s[p];if(T<=2&&S){let t=100;if(2===T&&Cs(l)){const e=l[1]-l[0];t=Math.abs(e)}const e={...v};void 0!==w&&(e.duration=V(w));const n=F(e,t,S);x=n.ease,w=n.duration}null!=w||(w=o);const b=h+P;1===c.length&&0===c[0]&&(c[1]=1);const A=c.length-l.length;if(A>0&&Zn(c,A),1===l.length&&l.unshift(null),m){w=vs(w,m);const t=[...l],e=[...c];x=Array.isArray(x)?[...x]:[x];const n=[...x];for(let i=0;i<m;i++){l.push(...t);for(let s=0;s<t.length;s++)c.push(e[s]+(i+1)),x.push(0===s?"linear":fs(n,s-1))}Ps(c,m)}const E=b+w;ws(i,l,x,c,b,E),g=Math.max(P+w,g),d=Math.max(E,d)};if(Mt(p)){y(m,f,bs("default",Ss(p,a)))}else{const t=ys(p,m,i,l),e=t.length;for(let n=0;n<e;n++){m=m,f=f;const i=Ss(t[n],a);for(const t in m)y(m[t],Es(f,t),bs(t,i),n,e)}}c=h,h+=g}return a.forEach((t,i)=>{for(const s in t){const o=t[s];o.sort(Ts);const a=[],l=[],u=[];for(let t=0;t<o.length;t++){const{at:e,value:n,easing:i}=o[t];a.push(n),l.push(C(0,d,e)),u.push(i||"easeOut")}0!==l[0]&&(l.unshift(0),a.unshift(a[0]),u.unshift("easeInOut")),1!==l[l.length-1]&&(l.push(1),a.push(null)),r.has(i)||r.set(i,{keyframes:{},transition:{}});const c=r.get(i);c.keyframes[s]=a,c.transition[s]={...e,duration:d,ease:u,times:l,...n}}}),r}(t,e,n,{spring:zn}).forEach(({keyframes:t,transition:e},n)=>{i.push(...bo(n,t,e))}),i}function Eo(t){return function(e,n,i){let s=[];var o;o=e,s=Array.isArray(o)&&o.some(Array.isArray)?Ao(e,n,t):bo(e,n,i,t);const r=new k(s);return t&&t.animations.push(r),r}}const Mo=Eo();function Co(t,e,n){t.style.setProperty("--"+e,n)}function Vo(t,e,n){t.style[e]=n}const Ro=M(()=>{try{document.createElement("div").animate({opacity:[1]})}catch(t){return!1}return!0}),Do=new WeakMap;function ko(t){const e=Do.get(t)||new Map;return Do.set(t,e),Do.get(t)}class Lo extends class{constructor(t){this.animation=t}get duration(){var t,e,n;const i=(null===(e=null===(t=this.animation)||void 0===t?void 0:t.effect)||void 0===e?void 0:e.getComputedTiming().duration)||(null===(n=this.options)||void 0===n?void 0:n.duration)||300;return R(Number(i))}get time(){var t;return this.animation?R((null===(t=this.animation)||void 0===t?void 0:t.currentTime)||0):0}set time(t){this.animation&&(this.animation.currentTime=V(t))}get speed(){return this.animation?this.animation.playbackRate:1}set speed(t){this.animation&&(this.animation.playbackRate=t)}get state(){return this.animation?this.animation.playState:"finished"}get startTime(){return this.animation?this.animation.startTime:null}get finished(){return this.animation?this.animation.finished:Promise.resolve()}play(){this.animation&&this.animation.play()}pause(){this.animation&&this.animation.pause()}stop(){this.animation&&"idle"!==this.state&&"finished"!==this.state&&(this.animation.commitStyles&&this.animation.commitStyles(),this.cancel())}flatten(){var t;this.animation&&(null===(t=this.animation.effect)||void 0===t||t.updateTiming({easing:"linear"}))}attachTimeline(t){return this.animation&&O(this.animation,t),b}complete(){this.animation&&this.animation.finish()}cancel(){try{this.animation&&this.animation.cancel()}catch(t){}}}{constructor(t,e,n,i){const s=e.startsWith("--");E("string"!=typeof i.type);const o=ko(t).get(e);o&&o.stop();if(Array.isArray(n)||(n=[n]),function(t,e,n){for(let i=0;i<e.length;i++)null===e[i]&&(e[i]=0===i?n():e[i-1]),"number"==typeof e[i]&&Se[t]&&(e[i]=Se[t].transform(e[i]));!Ro()&&e.length<2&&e.unshift(n())}(e,n,()=>e.startsWith("--")?t.style.getPropertyValue(e):window.getComputedStyle(t)[e]),j(i.type)){const t=F(i,100,i.type);i.ease=N()?t.ease:"easeOut",i.duration=V(t.duration),i.type="keyframes"}else i.ease=i.ease||"easeOut";const r=()=>{this.setValue(t,e,on(n,i)),this.cancel(),this.resolveFinishedPromise()},a=()=>{this.setValue=s?Co:Vo,this.options=i,this.updateFinishedPromise(),this.removeAnimation=()=>{const n=Do.get(t);n&&n.delete(e)}};ai()?(super(ri(t,e,n,i)),a(),!1===i.autoplay&&this.animation.pause(),this.animation.onfinish=r,ko(t).set(e,this)):(super(),a(),r())}then(t,e){return this.currentFinishedPromise.then(t,e)}updateFinishedPromise(){this.currentFinishedPromise=new Promise(t=>{this.resolveFinishedPromise=t})}play(){"finished"===this.state&&this.updateFinishedPromise(),super.play()}cancel(){this.removeAnimation(),super.cancel()}}const Bo=t=>function(e,n,i){return new k(function(t,e,n,i){const s=G(t,i),o=s.length,r=[];for(let t=0;t<o;t++){const i=s[t],a={...n};"function"==typeof a.delay&&(a.delay=a.delay(t,o));for(const t in e){const n=e[t],s={...L(a,t)};s.duration=s.duration?V(s.duration):s.duration,s.delay=V(s.delay||0),r.push(new Lo(i,t,n,s))}}return r}(e,n,i,t))},Fo=Bo();function jo(t,e){let n;const i=()=>{const{currentTime:i}=e,s=(null===i?0:i.value)/100;n!==s&&t(s),n=s};return lt.update(i,!0),()=>ut(i)}const Oo=new WeakMap;let Io;function Uo({target:t,contentRect:e,borderBoxSize:n}){var i;null===(i=Oo.get(t))||void 0===i||i.forEach(i=>{i({target:t,contentSize:e,get size(){return function(t,e){if(e){const{inlineSize:t,blockSize:n}=e[0];return{width:t,height:n}}return t instanceof SVGElement&&"getBBox"in t?t.getBBox():{width:t.offsetWidth,height:t.offsetHeight}}(t,n)}})})}function Wo(t){t.forEach(Uo)}function No(t,e){Io||"undefined"!=typeof ResizeObserver&&(Io=new ResizeObserver(Wo));const n=G(t);return n.forEach(t=>{let n=Oo.get(t);n||(n=new Set,Oo.set(t,n)),n.add(e),null==Io||Io.observe(t)}),()=>{n.forEach(t=>{const n=Oo.get(t);null==n||n.delete(e),(null==n?void 0:n.size)||null==Io||Io.unobserve(t)})}}const zo=new Set;let $o;function Ho(t){return zo.add(t),$o||($o=()=>{const t={width:window.innerWidth,height:window.innerHeight},e={target:window,size:t,contentSize:t};zo.forEach(t=>t(e))},window.addEventListener("resize",$o)),()=>{zo.delete(t),!zo.size&&$o&&($o=void 0)}}const Yo={x:{length:"Width",position:"Left"},y:{length:"Height",position:"Top"}};function Xo(t,e,n,i){const s=n[e],{length:o,position:r}=Yo[e],a=s.current,l=n.time;s.current=t["scroll"+r],s.scrollLength=t["scroll"+o]-t["client"+o],s.offset.length=0,s.offset[0]=0,s.offset[1]=s.scrollLength,s.progress=C(0,s.scrollLength,s.current);const u=i-l;s.velocity=u>50?0:Pt(s.current-a,u)}const Ko={start:0,center:.5,end:1};function Go(t,e,n=0){let i=0;if(t in Ko&&(t=Ko[t]),"string"==typeof t){const e=parseFloat(t);t.endsWith("px")?i=e:t.endsWith("%")?t=e/100:t.endsWith("vw")?i=e/100*document.documentElement.clientWidth:t.endsWith("vh")?i=e/100*document.documentElement.clientHeight:t=e}return"number"==typeof t&&(i=e*t),n+i}const _o=[0,0];function qo(t,e,n,i){let s=Array.isArray(t)?t:_o,o=0,r=0;return"number"==typeof t?s=[t,t]:"string"==typeof t&&(s=(t=t.trim()).includes(" ")?t.split(" "):[t,Ko[t]?t:"0"]),o=Go(s[0],n,i),r=Go(s[1],e),o-r}const Zo={Enter:[[0,1],[1,1]],Exit:[[0,0],[1,0]],Any:[[1,0],[0,1]],All:[[0,0],[1,1]]},Jo={x:0,y:0};function Qo(t,e,n){const{offset:i=Zo.All}=n,{target:s=t,axis:o="y"}=n,r="y"===o?"height":"width",a=s!==t?function(t,e){const n={x:0,y:0};let i=t;for(;i&&i!==e;)if(i instanceof HTMLElement)n.x+=i.offsetLeft,n.y+=i.offsetTop,i=i.offsetParent;else if("svg"===i.tagName){const t=i.getBoundingClientRect();i=i.parentElement;const e=i.getBoundingClientRect();n.x+=t.left-e.left,n.y+=t.top-e.top}else{if(!(i instanceof SVGGraphicsElement))break;{const{x:t,y:e}=i.getBBox();n.x+=t,n.y+=e;let s=null,o=i.parentNode;for(;!s;)"svg"===o.tagName&&(s=o),o=i.parentNode;i=s}}return n}(s,t):Jo,l=s===t?{width:t.scrollWidth,height:t.scrollHeight}:function(t){return"getBBox"in t&&"svg"!==t.tagName?t.getBBox():{width:t.clientWidth,height:t.clientHeight}}(s),u={width:t.clientWidth,height:t.clientHeight};e[o].offset.length=0;let c=!e[o].interpolate;const h=i.length;for(let t=0;t<h;t++){const n=qo(i[t],u[r],l[r],a[o]);c||n===e[o].interpolatorOffsets[t]||(c=!0),e[o].offset[t]=n}c&&(e[o].interpolate=qn(e[o].offset,Jn(i),{clamp:!1}),e[o].interpolatorOffsets=[...e[o].offset]),e[o].progress=Yt(0,1,e[o].interpolate(e[o].current))}function tr(t,e,n,i={}){return{measure:()=>function(t,e=t,n){if(n.x.targetOffset=0,n.y.targetOffset=0,e!==t){let i=e;for(;i&&i!==t;)n.x.targetOffset+=i.offsetLeft,n.y.targetOffset+=i.offsetTop,i=i.offsetParent}n.x.targetLength=e===t?e.scrollWidth:e.clientWidth,n.y.targetLength=e===t?e.scrollHeight:e.clientHeight,n.x.containerLength=t.clientWidth,n.y.containerLength=t.clientHeight}(t,i.target,n),update:e=>{!function(t,e,n){Xo(t,"x",e,n),Xo(t,"y",e,n),e.time=n}(t,n,e),(i.offset||i.target)&&Qo(t,n,i)},notify:()=>e(n)}}const er=new WeakMap,nr=new WeakMap,ir=new WeakMap,sr=t=>t===document.documentElement?window:t;function or(t,{container:e=document.documentElement,...n}={}){let i=ir.get(e);i||(i=new Set,ir.set(e,i));const s=tr(e,t,{time:0,x:{current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0},y:{current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0}},n);if(i.add(s),!er.has(e)){const t=()=>{for(const t of i)t.measure()},n=()=>{for(const t of i)t.update(ct.timestamp)},s=()=>{for(const t of i)t.notify()},a=()=>{lt.read(t,!1,!0),lt.read(n,!1,!0),lt.update(s,!1,!0)};er.set(e,a);const l=sr(e);window.addEventListener("resize",a,{passive:!0}),e!==document.documentElement&&nr.set(e,(r=a,"function"==typeof(o=e)?Ho(o):No(o,r))),l.addEventListener("scroll",a,{passive:!0})}var o,r;const a=er.get(e);return lt.read(a,!1,!0),()=>{var t;ut(a);const n=ir.get(e);if(!n)return;if(n.delete(s),n.size)return;const i=er.get(e);er.delete(e),i&&(sr(e).removeEventListener("scroll",i),null===(t=nr.get(e))||void 0===t||t(),window.removeEventListener("resize",i))}}const rr=new Map;function ar({source:t,container:e=document.documentElement,axis:n="y"}={}){t&&(e=t),rr.has(e)||rr.set(e,{});const i=rr.get(e);return i[n]||(i[n]=D()?new ScrollTimeline({source:e,axis:n}):function({source:t,container:e,axis:n="y"}){t&&(e=t);const i={value:0},s=or(t=>{i.value=100*t[n].progress},{container:e,axis:n});return{currentTime:i,cancel:s}}({source:e,axis:n})),i[n]}function lr(t){return t&&(t.target||t.offset)}function ur(t,{axis:e="y",...n}={}){const i={axis:e,...n};return"function"==typeof t?function(t,e){return function(t){return 2===t.length}(t)||lr(e)?or(n=>{t(n[e.axis].progress,n)},e):jo(t,ar(e))}(t,i):function(t,e){if(t.flatten(),lr(e))return t.pause(),or(n=>{t.time=t.duration*n[e.axis].progress},e);{const n=ar(e);return t.attachTimeline?t.attachTimeline(n,t=>(t.pause(),jo(e=>{t.time=t.duration*e},n))):b}}(t,i)}const cr={some:0,all:1};function hr(t,e,{root:n,margin:i,amount:s="some"}={}){const o=G(t),r=new WeakMap,a=new IntersectionObserver(t=>{t.forEach(t=>{const n=r.get(t.target);if(t.isIntersecting!==Boolean(n))if(t.isIntersecting){const n=e(t);"function"==typeof n?r.set(t.target,n):a.unobserve(t.target)}else"function"==typeof n&&(n(t),r.delete(t.target))})},{root:n,rootMargin:i,threshold:"number"==typeof s?s:cr[s]});return o.forEach(t=>a.observe(t)),()=>a.disconnect()}function dr(t,e){const n=yt.now(),i=({timestamp:s})=>{const o=s-n;o>=e&&(ut(i),t(o-e))};return lt.read(i,!0),()=>ut(i)}const pr=(t,e)=>Math.abs(t-e);function mr(t,e){const n=pr(t.x,e.x),i=pr(t.y,e.y);return Math.sqrt(n**2+i**2)}function fr(...t){const e=!Array.isArray(t[0]),n=e?0:-1,i=t[0+n],s=t[1+n],o=t[2+n],r=t[3+n],a=qn(s,o,{mixer:(l=o[0],(t=>t&&"object"==typeof t&&t.mix)(l)?l.mix:void 0),...r});var l;return e?a(i):a}const gr=lt,yr=rt.reduce((t,e)=>(t[e]=t=>ut(t),t),{}),{schedule:vr,cancel:xr}=at(queueMicrotask,!1),wr=(t,e)=>t.depth-e.depth;class Pr{constructor(){this.children=[],this.isDirty=!1}add(t){vt(this.children,t),this.isDirty=!0}remove(t){xt(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(wr),this.isDirty=!1,this.children.forEach(t)}}const Tr=["TopLeft","TopRight","BottomLeft","BottomRight"],Sr=Tr.length,br=t=>"string"==typeof t?parseFloat(t):t,Ar=t=>"number"==typeof t||re.test(t);function Er(t,e){return void 0!==t[e]?t[e]:t.borderRadius}const Mr=Vr(0,.5,zt),Cr=Vr(.5,.95,b);function Vr(t,e,n){return i=>i<t?0:i>e?1:n(C(t,e,i))}function Rr(t,e){t.min=e.min,t.max=e.max}function Dr(t,e){Rr(t.x,e.x),Rr(t.y,e.y)}function kr(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function Lr(t,e,n,i,s){return t=ho(t-=e,1/n,i),void 0!==s&&(t=ho(t,1/s,i)),t}function Br(t,e,[n,i,s],o,r){!function(t,e=0,n=1,i=.5,s,o=t,r=t){if(oe.test(e)){e=parseFloat(e);e=an(r.min,r.max,e/100)-r.min}if("number"!=typeof e)return;let a=an(o.min,o.max,i);t===o&&(a-=e),t.min=Lr(t.min,e,n,a,s),t.max=Lr(t.max,e,n,a,s)}(t,e[n],e[i],e[s],e.scale,o,r)}const Fr=["x","scaleX","originX"],jr=["y","scaleY","originY"];function Or(t,e,n,i){Br(t.x,e,Fr,n?n.x:void 0,i?i.x:void 0),Br(t.y,e,jr,n?n.y:void 0,i?i.y:void 0)}function Ir(t){return 0===t.translate&&1===t.scale}function Ur(t){return Ir(t.x)&&Ir(t.y)}function Wr(t,e){return t.min===e.min&&t.max===e.max}function Nr(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function zr(t,e){return Nr(t.x,e.x)&&Nr(t.y,e.y)}function $r(t){return ki(t.x)/ki(t.y)}function Hr(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class Yr{constructor(){this.members=[]}add(t){vt(this.members,t),t.scheduleRender()}remove(t){if(xt(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){const e=this.members.findIndex(e=>t===e);if(0===e)return!1;let n;for(let t=e;t>=0;t--){const e=this.members[t];if(!1!==e.isPresent){n=e;break}}return!!n&&(this.promote(n),!0)}promote(t,e){const n=this.lead;if(t!==n&&(this.prevLead=n,this.lead=t,t.show(),n)){n.instance&&n.scheduleRender(),t.scheduleRender(),t.resumeFrom=n,e&&(t.resumeFrom.preserveOpacity=!0),n.snapshot&&(t.snapshot=n.snapshot,t.snapshot.latestValues=n.animationValues||n.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:i}=t.options;!1===i&&n.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:e,resumingFrom:n}=t;e.onExitComplete&&e.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function Xr(t){return[t("x"),t("y")]}const Kr={hasAnimatedSinceResize:!0,hasEverUpdated:!1},Gr={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0},_r="undefined"!=typeof window&&void 0!==window.MotionDebug,qr=["","X","Y","Z"],Zr={visibility:"hidden"};let Jr=0;function Qr(t,e,n,i){const{latestValues:s}=e;s[t]&&(n[t]=s[t],e.setStaticValue(t,0),i&&(i[t]=0))}function ta({attachResizeListener:t,defaultParent:e,measureScroll:n,checkIsScrollRoot:i,resetTransform:s}){return class{constructor(t={},n=(null==e?void 0:e())){this.id=Jr++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,_r&&(Gr.totalNodes=Gr.resolvedTargetDeltas=Gr.recalculatedProjection=0),this.nodes.forEach(ia),this.nodes.forEach(ca),this.nodes.forEach(ha),this.nodes.forEach(sa),_r&&window.MotionDebug.record(Gr)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=n?n.root||n:this,this.path=n?[...n.path,n]:[],this.parent=n,this.depth=n?n.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new Pr)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new wt),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){const n=this.eventHandlers.get(t);n&&n.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e,n=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=Rs(e),this.instance=e;const{layoutId:i,layout:s,visualElement:o}=this.options;if(o&&!o.current&&o.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),n&&(s||i)&&(this.isLayoutDirty=!0),t){let n;const i=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,n&&n(),n=dr(i,250),Kr.hasAnimatedSinceResize&&(Kr.hasAnimatedSinceResize=!1,this.nodes.forEach(ua))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&o&&(i||s)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeTargetChanged:n,layout:i})=>{if(this.isTreeAnimationBlocked())return this.target=void 0,void(this.relativeTarget=void 0);const s=this.options.transition||o.getDefaultTransition()||ya,{onLayoutAnimationStart:r,onLayoutAnimationComplete:a}=o.getProps(),l=!this.targetLayout||!zr(this.targetLayout,i)||n,u=!e&&n;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||u||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,u);const e={...L(s,"layout"),onPlay:r,onComplete:a};(o.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else e||ua(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=i})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,ut(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(da),this.animationId++)}getTransformTemplate(){const{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked())return void(this.options.onExitComplete&&this.options.onExitComplete());if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;const{visualElement:n}=e.options;if(!n)return;const i=Dt(n);if(window.MotionHasOptimisedAnimation(i,"transform")){const{layout:t,layoutId:n}=e.options;window.MotionCancelOptimisedAnimation(i,"transform",lt,!(t||n))}const{parent:s}=e;s&&!s.hasCheckedOptimisedAppear&&t(s)}(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){const e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}const{layoutId:e,layout:n}=this.options;if(void 0===e&&!n)return;const i=this.getTransformTemplate();this.prevTransformTemplateValue=i?i(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){this.updateScheduled=!1;if(this.isUpdateBlocked())return this.unblockUpdate(),this.clearAllSnapshots(),void this.nodes.forEach(ra);this.isUpdating||this.nodes.forEach(aa),this.isUpdating=!1,this.nodes.forEach(la),this.nodes.forEach(ea),this.nodes.forEach(na),this.clearAllSnapshots();const t=yt.now();ct.delta=Yt(0,1e3/60,t-ct.timestamp),ct.timestamp=t,ct.isProcessing=!0,ht.update.process(ct),ht.preRender.process(ct),ht.render.process(ct),ct.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,vr.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(oa),this.sharedNodes.forEach(pa)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,lt.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){lt.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure())}updateLayout(){if(!this.instance)return;if(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead()||this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++){this.path[t].updateScroll()}const t=this.layout;this.layout=this.measure(!1),this.layoutCorrected={x:{min:0,max:0},y:{min:0,max:0}},this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=Boolean(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e){const e=i(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!s)return;const t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!Ur(this.projectionDelta),n=this.getTransformTemplate(),i=n?n(this.latestValues,""):void 0,o=i!==this.prevTransformTemplateValue;t&&(e||lo(this.latestValues)||o)&&(s(this.instance,i),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){const e=this.measurePageBox();let n=this.removeElementScroll(e);var i;return t&&(n=this.removeTransform(n)),wa((i=n).x),wa(i.y),{animationId:this.root.animationId,measuredBox:e,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){var t;const{visualElement:e}=this.options;if(!e)return{x:{min:0,max:0},y:{min:0,max:0}};const n=e.measureViewportBox();if(!((null===(t=this.scroll)||void 0===t?void 0:t.wasRoot)||this.path.some(Ta))){const{scroll:t}=this.root;t&&(go(n.x,t.offset.x),go(n.y,t.offset.y))}return n}removeElementScroll(t){var e;const n={x:{min:0,max:0},y:{min:0,max:0}};if(Dr(n,t),null===(e=this.scroll)||void 0===e?void 0:e.wasRoot)return n;for(let e=0;e<this.path.length;e++){const i=this.path[e],{scroll:s,options:o}=i;i!==this.root&&s&&o.layoutScroll&&(s.wasRoot&&Dr(n,t),go(n.x,s.offset.x),go(n.y,s.offset.y))}return n}applyTransform(t,e=!1){const n={x:{min:0,max:0},y:{min:0,max:0}};Dr(n,t);for(let t=0;t<this.path.length;t++){const i=this.path[t];!e&&i.options.layoutScroll&&i.scroll&&i!==i.root&&vo(n,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),lo(i.latestValues)&&vo(n,i.latestValues)}return lo(this.latestValues)&&vo(n,this.latestValues),n}removeTransform(t){const e={x:{min:0,max:0},y:{min:0,max:0}};Dr(e,t);for(let t=0;t<this.path.length;t++){const n=this.path[t];if(!n.instance)continue;if(!lo(n.latestValues))continue;ao(n.latestValues)&&n.updateSnapshot();const i={x:{min:0,max:0},y:{min:0,max:0}};Dr(i,n.measurePageBox()),Or(e,n.latestValues,n.snapshot?n.snapshot.layoutBox:void 0,i)}return lo(this.latestValues)&&Or(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==ct.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){var e;const n=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=n.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=n.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=n.isSharedProjectionDirty);const i=Boolean(this.resumingFrom)||this!==n;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||(null===(e=this.parent)||void 0===e?void 0:e.isProjectionDirty)||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:s,layoutId:o}=this.options;if(this.layout&&(s||o)){if(this.resolvedRelativeTargetAt=ct.timestamp,!this.targetDelta&&!this.relativeTarget){const t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},Oi(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),Dr(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){var r,a,l;if(this.target||(this.target={x:{min:0,max:0},y:{min:0,max:0}},this.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}}),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),r=this.target,a=this.relativeTarget,l=this.relativeParent.target,Fi(r.x,a.x,l.x),Fi(r.y,a.y,l.y)):this.targetDelta?(Boolean(this.resumingFrom)?this.target=this.applyTransform(this.layout.layoutBox):Dr(this.target,this.layout.layoutBox),fo(this.target,this.targetDelta)):Dr(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const t=this.getClosestProjectingParent();t&&Boolean(t.resumingFrom)===Boolean(this.resumingFrom)&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},Oi(this.relativeTargetOrigin,this.target,t.target),Dr(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}_r&&Gr.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(this.parent&&!ao(this.parent.latestValues)&&!uo(this.parent.latestValues))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return Boolean((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var t;const e=this.getLead(),n=Boolean(this.resumingFrom)||this!==e;let i=!0;if((this.isProjectionDirty||(null===(t=this.parent)||void 0===t?void 0:t.isProjectionDirty))&&(i=!1),n&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===ct.timestamp&&(i=!1),i)return;const{layout:s,layoutId:o}=this.options;if(this.isTreeAnimating=Boolean(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!s&&!o)return;Dr(this.layoutCorrected,this.layout.layoutBox);const r=this.treeScale.x,a=this.treeScale.y;!function(t,e,n,i=!1){const s=n.length;if(!s)return;let o,r;e.x=e.y=1;for(let a=0;a<s;a++){o=n[a],r=o.projectionDelta;const{visualElement:s}=o.options;s&&s.props.style&&"contents"===s.props.style.display||(i&&o.options.layoutScroll&&o.scroll&&o!==o.root&&vo(t,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,fo(t,r)),i&&lo(o.latestValues)&&vo(t,o.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}(this.layoutCorrected,this.treeScale,this.path,n),!e.layout||e.target||1===this.treeScale.x&&1===this.treeScale.y||(e.target=e.layout.layoutBox,e.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}});const{target:l}=e;l?(this.projectionDelta&&this.prevProjectionDelta?(kr(this.prevProjectionDelta.x,this.projectionDelta.x),kr(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),Bi(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.treeScale.x===r&&this.treeScale.y===a&&Hr(this.projectionDelta.x,this.prevProjectionDelta.x)&&Hr(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),_r&&Gr.recalculatedProjection++):this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender())}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){var e;if(null===(e=this.options.visualElement)||void 0===e||e.scheduleRender(),t){const t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDeltaWithTransform={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}}}setAnimationOrigin(t,e=!1){const n=this.snapshot,i=n?n.latestValues:{},s={...this.latestValues},o={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;const r={x:{min:0,max:0},y:{min:0,max:0}},a=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),l=this.getStack(),u=!l||l.members.length<=1,c=Boolean(a&&!u&&!0===this.options.crossfade&&!this.path.some(ga));let h;this.animationProgress=0,this.mixTargetDelta=e=>{const n=e/1e3;var l,d;ma(o.x,t.x,n),ma(o.y,t.y,n),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Oi(r,this.layout.layoutBox,this.relativeParent.layout.layoutBox),function(t,e,n,i){fa(t.x,e.x,n.x,i),fa(t.y,e.y,n.y,i)}(this.relativeTarget,this.relativeTargetOrigin,r,n),h&&(l=this.relativeTarget,d=h,Wr(l.x,d.x)&&Wr(l.y,d.y))&&(this.isProjectionDirty=!1),h||(h={x:{min:0,max:0},y:{min:0,max:0}}),Dr(h,this.relativeTarget)),a&&(this.animationValues=s,function(t,e,n,i,s,o){s?(t.opacity=an(0,void 0!==n.opacity?n.opacity:1,Mr(i)),t.opacityExit=an(void 0!==e.opacity?e.opacity:1,0,Cr(i))):o&&(t.opacity=an(void 0!==e.opacity?e.opacity:1,void 0!==n.opacity?n.opacity:1,i));for(let s=0;s<Sr;s++){const o=`border${Tr[s]}Radius`;let r=Er(e,o),a=Er(n,o);if(void 0===r&&void 0===a)continue;r||(r=0),a||(a=0);0===r||0===a||Ar(r)===Ar(a)?(t[o]=Math.max(an(br(r),br(a),i),0),(oe.test(a)||oe.test(r))&&(t[o]+="%")):t[o]=a}(e.rotate||n.rotate)&&(t.rotate=an(e.rotate||0,n.rotate||0,i))}(s,i,this.latestValues,n,c,u)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(ut(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=lt.update(()=>{Kr.hasAnimatedSinceResize=!0,this.currentAnimation=ps(0,1e3,{...t,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onComplete:()=>{t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const t=this.getLead();let{targetWithTransforms:e,target:n,layout:i,latestValues:s}=t;if(e&&n&&i){if(this!==t&&this.layout&&i&&Pa(this.options.animationType,this.layout.layoutBox,i.layoutBox)){n=this.target||{x:{min:0,max:0},y:{min:0,max:0}};const e=ki(this.layout.layoutBox.x);n.x.min=t.target.x.min,n.x.max=n.x.min+e;const i=ki(this.layout.layoutBox.y);n.y.min=t.target.y.min,n.y.max=n.y.min+i}Dr(e,n),vo(e,s),Bi(this.projectionDeltaWithTransform,this.layoutCorrected,e,s)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new Yr);this.sharedNodes.get(t).add(e);const n=e.options.initialPromotionConfig;e.promote({transition:n?n.transition:void 0,preserveFollowOpacity:n&&n.shouldPreserveFollowOpacity?n.shouldPreserveFollowOpacity(e):void 0})}isLead(){const t=this.getStack();return!t||t.lead===this}getLead(){var t;const{layoutId:e}=this.options;return e&&(null===(t=this.getStack())||void 0===t?void 0:t.lead)||this}getPrevLead(){var t;const{layoutId:e}=this.options;return e?null===(t=this.getStack())||void 0===t?void 0:t.prevLead:void 0}getStack(){const{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:n}={}){const i=this.getStack();i&&i.promote(this,n),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){const t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){const{visualElement:t}=this.options;if(!t)return;let e=!1;const{latestValues:n}=t;if((n.z||n.rotate||n.rotateX||n.rotateY||n.rotateZ||n.skewX||n.skewY)&&(e=!0),!e)return;const i={};n.z&&Qr("z",t,i,this.animationValues);for(let e=0;e<qr.length;e++)Qr("rotate"+qr[e],t,i,this.animationValues),Qr("skew"+qr[e],t,i,this.animationValues);t.render();for(const e in i)t.setStaticValue(e,i[e]),this.animationValues&&(this.animationValues[e]=i[e]);t.scheduleRender()}getProjectionStyles(t){var e,n;if(!this.instance||this.isSVG)return;if(!this.isVisible)return Zr;const i={visibility:""},s=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,i.opacity="",i.pointerEvents=Ki(null==t?void 0:t.pointerEvents)||"",i.transform=s?s(this.latestValues,""):"none",i;const o=this.getLead();if(!this.projectionDelta||!this.layout||!o.target){const e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=Ki(null==t?void 0:t.pointerEvents)||""),this.hasProjected&&!lo(this.latestValues)&&(e.transform=s?s({},""):"none",this.hasProjected=!1),e}const r=o.animationValues||o.latestValues;this.applyTransformsToTarget(),i.transform=function(t,e,n){let i="";const s=t.x.translate/e.x,o=t.y.translate/e.y,r=(null==n?void 0:n.z)||0;if((s||o||r)&&(i=`translate3d(${s}px, ${o}px, ${r}px) `),1===e.x&&1===e.y||(i+=`scale(${1/e.x}, ${1/e.y}) `),n){const{transformPerspective:t,rotate:e,rotateX:s,rotateY:o,skewX:r,skewY:a}=n;t&&(i=`perspective(${t}px) ${i}`),e&&(i+=`rotate(${e}deg) `),s&&(i+=`rotateX(${s}deg) `),o&&(i+=`rotateY(${o}deg) `),r&&(i+=`skewX(${r}deg) `),a&&(i+=`skewY(${a}deg) `)}const a=t.x.scale*e.x,l=t.y.scale*e.y;return 1===a&&1===l||(i+=`scale(${a}, ${l})`),i||"none"}(this.projectionDeltaWithTransform,this.treeScale,r),s&&(i.transform=s(r,i.transform));const{x:a,y:l}=this.projectionDelta;i.transformOrigin=`${100*a.origin}% ${100*l.origin}% 0`,o.animationValues?i.opacity=o===this?null!==(n=null!==(e=r.opacity)&&void 0!==e?e:this.latestValues.opacity)&&void 0!==n?n:1:this.preserveOpacity?this.latestValues.opacity:r.opacityExit:i.opacity=o===this?void 0!==r.opacity?r.opacity:"":void 0!==r.opacityExit?r.opacityExit:0;for(const t in Qs){if(void 0===r[t])continue;const{correct:e,applyTo:n}=Qs[t],s="none"===i.transform?r[t]:e(r[t],o);if(n){const t=n.length;for(let e=0;e<t;e++)i[n[e]]=s}else i[t]=s}return this.options.layoutId&&(i.pointerEvents=o===this?Ki(null==t?void 0:t.pointerEvents)||"":"none"),i}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>{var e;return null===(e=t.currentAnimation)||void 0===e?void 0:e.stop()}),this.root.nodes.forEach(ra),this.root.sharedNodes.clear()}}}function ea(t){t.updateLayout()}function na(t){var e;const n=(null===(e=t.resumeFrom)||void 0===e?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&n&&t.hasListeners("didUpdate")){const{layoutBox:e,measuredBox:i}=t.layout,{animationType:s}=t.options,o=n.source!==t.layout.source;"size"===s?Xr(t=>{const i=o?n.measuredBox[t]:n.layoutBox[t],s=ki(i);i.min=e[t].min,i.max=i.min+s}):Pa(s,n.layoutBox,e)&&Xr(i=>{const s=o?n.measuredBox[i]:n.layoutBox[i],r=ki(e[i]);s.max=s.min+r,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[i].max=t.relativeTarget[i].min+r)});const r={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};Bi(r,e,n.layoutBox);const a={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};o?Bi(a,t.applyTransform(i,!0),n.measuredBox):Bi(a,e,n.layoutBox);const l=!Ur(r);let u=!1;if(!t.resumeFrom){const i=t.getClosestProjectingParent();if(i&&!i.resumeFrom){const{snapshot:s,layout:o}=i;if(s&&o){const r={x:{min:0,max:0},y:{min:0,max:0}};Oi(r,n.layoutBox,s.layoutBox);const a={x:{min:0,max:0},y:{min:0,max:0}};Oi(a,e,o.layoutBox),zr(r,a)||(u=!0),i.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=r,t.relativeParent=i)}}}t.notifyListeners("didUpdate",{layout:e,snapshot:n,delta:a,layoutDelta:r,hasLayoutChanged:l,hasRelativeTargetChanged:u})}else if(t.isLead()){const{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function ia(t){_r&&Gr.totalNodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=Boolean(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function sa(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function oa(t){t.clearSnapshot()}function ra(t){t.clearMeasurements()}function aa(t){t.isLayoutDirty=!1}function la(t){const{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function ua(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function ca(t){t.resolveTargetDelta()}function ha(t){t.calcProjection()}function da(t){t.resetSkewAndRotation()}function pa(t){t.removeLeadSnapshot()}function ma(t,e,n){t.translate=an(e.translate,0,n),t.scale=an(e.scale,1,n),t.origin=e.origin,t.originPoint=e.originPoint}function fa(t,e,n,i){t.min=an(e.min,n.min,i),t.max=an(e.max,n.max,i)}function ga(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}const ya={duration:.45,ease:[.4,0,.1,1]},va=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),xa=va("applewebkit/")&&!va("chrome/")?Math.round:b;function wa(t){t.min=xa(t.min),t.max=xa(t.max)}function Pa(t,e,n){return"position"===t||"preserve-aspect"===t&&(i=$r(e),s=$r(n),o=.2,!(Math.abs(i-s)<=o));var i,s,o}function Ta(t){var e;return t!==t.root&&(null===(e=t.scroll)||void 0===e?void 0:e.wasRoot)}const Sa=ta({attachResizeListener:(t,e)=>qi(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),ba={current:void 0},Aa=ta({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!ba.current){const t=new Sa({});t.mount(window),t.setOptions({layoutScroll:!0}),ba.current=t}return ba.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>Boolean("fixed"===window.getComputedStyle(t).position)}),Ea=t=>!t.isLayoutDirty&&t.willUpdate(!1);function Ma(){const t=new Set,e=new WeakMap,n=()=>t.forEach(Ea);return{add:i=>{t.add(i),e.set(i,i.addEventListener("willUpdate",n))},remove:i=>{t.delete(i);const s=e.get(i);s&&(s(),e.delete(i)),n()},dirty:n}}function Ca(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}const Va={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t){if(!re.test(t))return t;t=parseFloat(t)}return`${Ca(t,e.target.x)}% ${Ca(t,e.target.y)}%`}},Ra={correct:(t,{treeScale:e,projectionDelta:n})=>{const i=t,s=ve.parse(t);if(s.length>5)return i;const o=ve.createTransformer(t),r="number"!=typeof s[0]?1:0,a=n.x.scale*e.x,l=n.y.scale*e.y;s[0+r]/=a,s[1+r]/=l;const u=an(a,l,.5);return"number"==typeof s[2+r]&&(s[2+r]/=u),"number"==typeof s[3+r]&&(s[3+r]/=u),o(s)}},Da=t=>!0===t,ka=({children:t,id:n,inherit:i=!0})=>{const s=e.useContext(m),o=e.useContext(ds),[r,a]=is(),l=e.useRef(null),u=s.id||o;null===l.current&&((t=>Da(!0===t)||"id"===t)(i)&&u&&(n=n?u+"-"+n:u),l.current={id:n,group:Da(i)&&s.group||Ma()});const c=e.useMemo(()=>({...l.current,forceRender:r}),[a]);return d(m.Provider,{value:c,children:t})},La=e.createContext({strict:!1});function Ba(t){for(const e in t)ks[e]={...ks[e],...t[e]}}function Fa(t){return"function"==typeof t}const ja=e.createContext(null);function Oa(t){if("undefined"==typeof Proxy)return t;const e=new Map;return new Proxy((...e)=>t(...e),{get:(n,i)=>"create"===i?t:(e.has(i)||e.set(i,t(i)),e.get(i))})}class Ia{constructor(t,e,{transformPagePoint:n,contextWindow:i,dragSnapToOrigin:s=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!this.lastMoveEvent||!this.lastMoveEventInfo)return;const t=Na(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,n=mr(t.offset,{x:0,y:0})>=3;if(!e&&!n)return;const{point:i}=t,{timestamp:s}=ct;this.history.push({...i,timestamp:s});const{onStart:o,onMove:r}=this.handlers;e||(o&&o(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),r&&r(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=Ua(e,this.transformPagePoint),lt.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();const{onEnd:n,onSessionEnd:i,resumeAnimation:s}=this.handlers;if(this.dragSnapToOrigin&&s&&s(),!this.lastMoveEvent||!this.lastMoveEventInfo)return;const o=Na("pointercancel"===t.type?this.lastMoveEventInfo:Ua(e,this.transformPagePoint),this.history);this.startEvent&&n&&n(t,o),i&&i(t,o)},!J(t))return;this.dragSnapToOrigin=s,this.handlers=e,this.transformPagePoint=n,this.contextWindow=i||window;const o=Ua(Zi(t),this.transformPagePoint),{point:r}=o,{timestamp:a}=ct;this.history=[{...r,timestamp:a}];const{onSessionStart:l}=e;l&&l(t,Na(o,this.history)),this.removeListeners=fn(Qi(this.contextWindow,"pointermove",this.handlePointerMove),Qi(this.contextWindow,"pointerup",this.handlePointerUp),Qi(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),ut(this.updatePoint)}}function Ua(t,e){return e?{point:e(t.point)}:t}function Wa(t,e){return{x:t.x-e.x,y:t.y-e.y}}function Na({point:t},e){return{point:t,delta:Wa(t,$a(e)),offset:Wa(t,za(e)),velocity:Ha(e,.1)}}function za(t){return t[0]}function $a(t){return t[t.length-1]}function Ha(t,e){if(t.length<2)return{x:0,y:0};let n=t.length-1,i=null;const s=$a(t);for(;n>=0&&(i=t[n],!(s.timestamp-i.timestamp>V(e)));)n--;if(!i)return{x:0,y:0};const o=R(s.timestamp-i.timestamp);if(0===o)return{x:0,y:0};const r={x:(s.x-i.x)/o,y:(s.y-i.y)/o};return r.x===1/0&&(r.x=0),r.y===1/0&&(r.y=0),r}function Ya(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}function Xa(t,e,n){return{min:void 0!==e?t.min+e:void 0,max:void 0!==n?t.max+n-(t.max-t.min):void 0}}function Ka(t,e){let n=e.min-t.min,i=e.max-t.max;return e.max-e.min<t.max-t.min&&([n,i]=[i,n]),{min:n,max:i}}const Ga=.35;function _a(t,e,n){return{min:qa(t,e),max:qa(t,n)}}function qa(t,e){return"number"==typeof t?t:t[e]||0}const Za=({current:t})=>t?t.ownerDocument.defaultView:null,Ja=new WeakMap;class Qa{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic={x:{min:0,max:0},y:{min:0,max:0}},this.visualElement=t}start(t,{snapToCursor:e=!1}={}){const{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;const{dragSnapToOrigin:i}=this.getProps();this.panSession=new Ia(t,{onSessionStart:t=>{const{dragSnapToOrigin:n}=this.getProps();n?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(Zi(t).point)},onStart:(t,e)=>{const{drag:n,dragPropagation:i,onDragStart:s}=this.getProps();if(n&&!i&&(this.openDragLock&&this.openDragLock(),this.openDragLock="x"===(o=n)||"y"===o?X[o]?null:(X[o]=!0,()=>{X[o]=!1}):X.x||X.y?null:(X.x=X.y=!0,()=>{X.x=X.y=!1}),!this.openDragLock))return;var o;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),Xr(t=>{let e=this.getAxisMotionValue(t).get()||0;if(oe.test(e)){const{projection:n}=this.visualElement;if(n&&n.layout){const i=n.layout.layoutBox[t];if(i){e=ki(i)*(parseFloat(e)/100)}}}this.originPoint[t]=e}),s&&lt.postRender(()=>s(t,e)),Ct(this.visualElement,"transform");const{animationState:r}=this.visualElement;r&&r.setActive("whileDrag",!0)},onMove:(t,e)=>{const{dragPropagation:n,dragDirectionLock:i,onDirectionLock:s,onDrag:o}=this.getProps();if(!n&&!this.openDragLock)return;const{offset:r}=e;if(i&&null===this.currentDirection)return this.currentDirection=function(t,e=10){let n=null;Math.abs(t.y)>e?n="y":Math.abs(t.x)>e&&(n="x");return n}(r),void(null!==this.currentDirection&&s&&s(this.currentDirection));this.updateAxis("x",e.point,r),this.updateAxis("y",e.point,r),this.visualElement.render(),o&&o(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>Xr(t=>{var e;return"paused"===this.getAnimationState(t)&&(null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.play())})},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:i,contextWindow:Za(this.visualElement)})}stop(t,e){const n=this.isDragging;if(this.cancel(),!n)return;const{velocity:i}=e;this.startAnimation(i);const{onDragEnd:s}=this.getProps();s&&lt.postRender(()=>s(t,e))}cancel(){this.isDragging=!1;const{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:n}=this.getProps();!n&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,n){const{drag:i}=this.getProps();if(!n||!tl(t,i,this.currentDirection))return;const s=this.getAxisMotionValue(t);let o=this.originPoint[t]+n[t];this.constraints&&this.constraints[t]&&(o=function(t,{min:e,max:n},i){return void 0!==e&&t<e?t=i?an(e,t,i.min):Math.max(t,e):void 0!==n&&t>n&&(t=i?an(n,t,i.max):Math.min(t,n)),t}(o,this.constraints[t],this.elastic[t])),s.set(o)}resolveConstraints(){var t;const{dragConstraints:e,dragElastic:n}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null===(t=this.visualElement.projection)||void 0===t?void 0:t.layout,s=this.constraints;e&&Ya(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):this.constraints=!(!e||!i)&&function(t,{top:e,left:n,bottom:i,right:s}){return{x:Xa(t.x,n,s),y:Xa(t.y,e,i)}}(i.layoutBox,e),this.elastic=function(t=Ga){return!1===t?t=0:!0===t&&(t=Ga),{x:_a(t,"left","right"),y:_a(t,"top","bottom")}}(n),s!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&Xr(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){const n={};return void 0!==e.min&&(n.min=e.min-t.min),void 0!==e.max&&(n.max=e.max-t.min),n}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:e}=this.getProps();if(!t||!Ya(t))return!1;const n=t.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const s=function(t,e,n){const i=xo(t,n),{scroll:s}=e;return s&&(go(i.x,s.offset.x),go(i.y,s.offset.y)),i}(n,i.root,this.visualElement.getTransformPagePoint());let o=function(t,e){return{x:Ka(t.x,e.x),y:Ka(t.y,e.y)}}(i.layout.layoutBox,s);if(e){const t=e(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=oo(t))}return o}startAnimation(t){const{drag:e,dragMomentum:n,dragElastic:i,dragTransition:s,dragSnapToOrigin:o,onDragTransitionEnd:r}=this.getProps(),a=this.constraints||{},l=Xr(r=>{if(!tl(r,e,this.currentDirection))return;let l=a&&a[r]||{};o&&(l={min:0,max:0});const u=i?200:1e6,c=i?40:1e7,h={type:"inertia",velocity:n?t[r]:0,bounceStiffness:u,bounceDamping:c,timeConstant:750,restDelta:1,restSpeed:10,...s,...l};return this.startAxisValueAnimation(r,h)});return Promise.all(l).then(r)}startAxisValueAnimation(t,e){const n=this.getAxisMotionValue(t);return Ct(this.visualElement,t),n.start(mi(t,n,0,e,this.visualElement,!1))}stopAnimation(){Xr(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){Xr(t=>{var e;return null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.pause()})}getAnimationState(t){var e;return null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.state}getAxisMotionValue(t){const e="_drag"+t.toUpperCase(),n=this.visualElement.getProps(),i=n[e];return i||this.visualElement.getValue(t,(n.initial?n.initial[t]:void 0)||0)}snapToCursor(t){Xr(e=>{const{drag:n}=this.getProps();if(!tl(e,n,this.currentDirection))return;const{projection:i}=this.visualElement,s=this.getAxisMotionValue(e);if(i&&i.layout){const{min:n,max:o}=i.layout.layoutBox[e];s.set(t[e]-an(n,o,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:e}=this.getProps(),{projection:n}=this.visualElement;if(!Ya(e)||!n||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};Xr(t=>{const e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){const n=e.get();i[t]=function(t,e){let n=.5;const i=ki(t),s=ki(e);return s>i?n=C(e.min,e.max-i,t.min):i>s&&(n=C(t.min,t.max-s,e.min)),Yt(0,1,n)}({min:n,max:n},this.constraints[t])}});const{transformTemplate:s}=this.visualElement.getProps();this.visualElement.current.style.transform=s?s({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),Xr(e=>{if(!tl(e,t,null))return;const n=this.getAxisMotionValue(e),{min:s,max:o}=this.constraints[e];n.set(an(s,o,i[e]))})}addListeners(){if(!this.visualElement.current)return;Ja.set(this.visualElement,this);const t=Qi(this.visualElement.current,"pointerdown",t=>{const{drag:e,dragListener:n=!0}=this.getProps();e&&n&&this.start(t)}),e=()=>{const{dragConstraints:t}=this.getProps();Ya(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:n}=this.visualElement,i=n.addEventListener("measure",e);n&&!n.layout&&(n.root&&n.root.updateScroll(),n.updateLayout()),lt.read(e);const s=qi(window,"resize",()=>this.scalePositionWithinConstraints()),o=n.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(Xr(e=>{const n=this.getAxisMotionValue(e);n&&(this.originPoint[e]+=t[e].translate,n.set(n.get()+t[e].translate))}),this.visualElement.render())});return()=>{s(),t(),i(),o&&o()}}getProps(){const t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:n=!1,dragPropagation:i=!1,dragConstraints:s=!1,dragElastic:o=Ga,dragMomentum:r=!0}=t;return{...t,drag:e,dragDirectionLock:n,dragPropagation:i,dragConstraints:s,dragElastic:o,dragMomentum:r}}}function tl(t,e,n){return!(!0!==e&&e!==t||null!==n&&n!==t)}const el=t=>(e,n)=>{t&&lt.postRender(()=>t(e,n))};const nl=e.createContext({});class il extends e.Component{componentDidMount(){const{visualElement:t,layoutGroup:e,switchLayoutGroup:n,layoutId:i}=this.props,{projection:s}=t;to(ol),s&&(e.group&&e.group.add(s),n&&n.register&&i&&n.register(s),s.root.didUpdate(),s.addEventListener("animationComplete",()=>{this.safeToRemove()}),s.setOptions({...s.options,onExitComplete:()=>this.safeToRemove()})),Kr.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:e,visualElement:n,drag:i,isPresent:s}=this.props,o=n.projection;return o?(o.isPresent=s,i||t.layoutDependency!==e||void 0===e?o.willUpdate():this.safeToRemove(),t.isPresent!==s&&(s?o.promote():o.relegate()||lt.postRender(()=>{const t=o.getStack();t&&t.members.length||this.safeToRemove()})),null):null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),vr.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:e,switchLayoutGroup:n}=this.props,{projection:i}=t;i&&(i.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(i),n&&n.deregister&&n.deregister(i))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function sl(t){const[n,i]=us(),s=e.useContext(m);return d(il,{...t,layoutGroup:s,switchLayoutGroup:e.useContext(nl),isPresent:n,safeToRemove:i})}const ol={borderRadius:{...Va,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:Va,borderTopRightRadius:Va,borderBottomLeftRadius:Va,borderBottomRightRadius:Va,boxShadow:Ra},rl={pan:{Feature:class extends Mi{constructor(){super(...arguments),this.removePointerDownListener=b}onPointerDown(t){this.session=new Ia(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:Za(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:e,onPan:n,onPanEnd:i}=this.node.getProps();return{onSessionStart:el(t),onStart:el(e),onMove:n,onEnd:(t,e)=>{delete this.session,i&&lt.postRender(()=>i(t,e))}}}mount(){this.removePointerDownListener=Qi(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}},drag:{Feature:class extends Mi{constructor(t){super(t),this.removeGroupControls=b,this.removeListeners=b,this.controls=new Qa(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||b}unmount(){this.removeGroupControls(),this.removeListeners()}},ProjectionNode:Aa,MeasureLayout:sl}};function al(t,e,n){const{props:i}=t;t.animationState&&i.whileHover&&t.animationState.setActive("whileHover","Start"===n);const s=i["onHover"+n];s&&lt.postRender(()=>s(e,Zi(e)))}function ll(t,e,n){const{props:i}=t;t.animationState&&i.whileTap&&t.animationState.setActive("whileTap","Start"===n);const s=i["onTap"+("End"===n?"":n)];s&&lt.postRender(()=>s(e,Zi(e)))}const ul=new WeakMap,cl=new WeakMap,hl=t=>{const e=ul.get(t.target);e&&e(t)},dl=t=>{t.forEach(hl)};function pl(t,e,n){const i=function({root:t,...e}){const n=t||document;cl.has(n)||cl.set(n,{});const i=cl.get(n),s=JSON.stringify(e);return i[s]||(i[s]=new IntersectionObserver(dl,{root:t,...e})),i[s]}(e);return ul.set(t,n),i.observe(t),()=>{ul.delete(t),i.unobserve(t)}}const ml={some:0,all:1};const fl={inView:{Feature:class extends Mi{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:e,margin:n,amount:i="some",once:s}=t,o={root:e?e.current:void 0,rootMargin:n,threshold:"number"==typeof i?i:ml[i]};return pl(this.node.current,o,t=>{const{isIntersecting:e}=t;if(this.isInView===e)return;if(this.isInView=e,s&&!e&&this.hasEnteredView)return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);const{onViewportEnter:n,onViewportLeave:i}=this.node.getProps(),o=e?n:i;o&&o(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;const{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return n=>t[n]!==e[n]}(t,e))&&this.startObserver()}unmount(){}}},tap:{Feature:class extends Mi{mount(){const{current:t}=this.node;t&&(this.unmount=st(t,t=>(ll(this.node,t,"Start"),(t,{success:e})=>ll(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}},focus:{Feature:class extends Mi{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=fn(qi(this.node.current,"focus",()=>this.onFocus()),qi(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}},hover:{Feature:class extends Mi{mount(){const{current:t}=this.node;t&&(this.unmount=function(t,e,n={}){const[i,s,o]=_(t,n),r=q(t=>{const{target:n}=t,i=e(t);if("function"!=typeof i||!n)return;const o=q(t=>{i(t),n.removeEventListener("pointerleave",o)});n.addEventListener("pointerleave",o,s)});return i.forEach(t=>{t.addEventListener("pointerenter",r,s)}),o}(t,t=>(al(this.node,t,"Start"),t=>al(this.node,t,"End"))))}unmount(){}}}},gl={layout:{ProjectionNode:Aa,MeasureLayout:sl}};function yl(t){const{initial:n,animate:i}=function(t,e){if(Hi(t)){const{initial:e,animate:n}=t;return{initial:!1===e||v(e)?e:void 0,animate:v(n)?n:void 0}}return!1!==t.inherit?e:{}}(t,e.useContext(Ri));return e.useMemo(()=>({initial:n,animate:i}),[vl(n),vl(i)])}function vl(t){return Array.isArray(t)?t.join(" "):t}const xl=Symbol.for("motionComponentSymbol");function wl(t,n,i){return e.useCallback(e=>{e&&t.onMount&&t.onMount(e),n&&(e?n.mount(e):n.unmount()),i&&("function"==typeof i?i(e):Ya(i)&&(i.current=e))},[n])}function Pl(t,n,i,s,o){var r,a;const{visualElement:l}=e.useContext(Ri),u=e.useContext(La),c=e.useContext($i),h=e.useContext(ss).reducedMotion,d=e.useRef(null);s=s||u.renderer,!d.current&&s&&(d.current=s(t,{visualState:n,parent:l,props:i,presenceContext:c,blockInitialAnimation:!!c&&!1===c.initial,reducedMotionConfig:h}));const p=d.current,m=e.useContext(nl);!p||p.projection||!o||"html"!==p.type&&"svg"!==p.type||function(t,e,n,i){const{layoutId:s,layout:o,drag:r,dragConstraints:a,layoutScroll:l,layoutRoot:u}=e;t.projection=new n(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){return e?!1!==e.options.allowProjection?e.projection:t(e.parent):void 0}(t.parent)),t.projection.setOptions({layoutId:s,layout:o,alwaysMeasureLayout:Boolean(r)||a&&Ya(a),visualElement:t,animationType:"string"==typeof o?o:"both",initialPromotionConfig:i,layoutScroll:l,layoutRoot:u})}(d.current,i,o,m);const f=e.useRef(!1);e.useInsertionEffect(()=>{p&&f.current&&p.update(i,c)});const g=i[Rt],y=e.useRef(Boolean(g)&&!(null===(r=window.MotionHandoffIsComplete)||void 0===r?void 0:r.call(window,g))&&(null===(a=window.MotionHasOptimisedAnimation)||void 0===a?void 0:a.call(window,g)));return ns(()=>{p&&(f.current=!0,window.MotionIsMounted=!0,p.updateFeatures(),vr.render(p.render),y.current&&p.animationState&&p.animationState.animateChanges())}),e.useEffect(()=>{p&&(!y.current&&p.animationState&&p.animationState.animateChanges(),y.current&&(queueMicrotask(()=>{var t;null===(t=window.MotionHandoffMarkAsComplete)||void 0===t||t.call(window,g)}),y.current=!1))}),p}function Tl({preloadedFeatures:t,createVisualElement:n,useRender:i,useVisualState:s,Component:o}){var r,a;function l(t,r){let a;const l={...e.useContext(ss),...t,layoutId:Sl(t)},{isStatic:u}=l,c=yl(t),h=s(t,u);if(!u&&ts){e.useContext(La).strict;const t=function(t){const{drag:e,layout:n}=ks;if(!e&&!n)return{};const i={...e,...n};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==n?void 0:n.isEnabled(t))?i.MeasureLayout:void 0,ProjectionNode:i.ProjectionNode}}(l);a=t.MeasureLayout,c.visualElement=Pl(o,h,l,n,t.ProjectionNode)}return p(Ri.Provider,{value:c,children:[a&&c.visualElement?d(a,{visualElement:c.visualElement,...l}):null,i(o,t,wl(h,c.visualElement,r),h,u,c.visualElement)]})}t&&Ba(t),l.displayName="motion."+("string"==typeof o?o:`create(${null!==(a=null!==(r=o.displayName)&&void 0!==r?r:o.name)&&void 0!==a?a:""})`);const u=e.forwardRef(l);return u[xl]=o,u}function Sl({layoutId:t}){const n=e.useContext(m).id;return n&&void 0!==t?n+"-"+t:t}const bl=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Al(t){return"string"==typeof t&&!t.includes("-")&&!!(bl.indexOf(t)>-1||/[A-Z]/u.test(t))}const El=()=>({style:{},transform:{},transformOrigin:{},vars:{}}),Ml=()=>({style:{},transform:{},transformOrigin:{},vars:{},attrs:{}});const Cl=["x","y","width","height","cx","cy","r"],Vl={useVisualState:Gi({scrapeMotionValuesFromProps:io,createRenderState:Ml,onUpdate:({props:t,prevProps:e,current:n,renderState:i,latestValues:s})=>{if(!n)return;let o=!!t.drag;if(!o)for(const t in s)if(pt.has(t)){o=!0;break}if(!o)return;let r=!e;if(e)for(let n=0;n<Cl.length;n++){const i=Cl[n];t[i]!==e[i]&&(r=!0)}r&&lt.read(()=>{!function(t,e){try{e.dimensions="function"==typeof t.getBBox?t.getBBox():t.getBoundingClientRect()}catch(t){e.dimensions={x:0,y:0,width:0,height:0}}}(n,i),lt.render(()=>{Gs(i,s,qs(n.tagName),t.transformTemplate),Js(n,i)})})}})},Rl={useVisualState:Gi({scrapeMotionValuesFromProps:no,createRenderState:El})};function Dl(t,e,n){for(const i in e)Mt(e[i])||eo(i,n)||(t[i]=e[i])}function kl(t,n){const i={};return Dl(i,t.style||{},t),Object.assign(i,function({transformTemplate:t},n){return e.useMemo(()=>{const e={style:{},transform:{},transformOrigin:{},vars:{}};return Hs(e,n,t),Object.assign({},e.vars,e.style)},[n])}(t,n)),i}function Ll(t,e){const n={},i=kl(t,e);return t.drag&&!1!==t.dragListener&&(n.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=!0===t.drag?"none":"pan-"+("x"===t.drag?"y":"x")),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(n.tabIndex=0),n.style=i,n}function Bl(t,n,i,s){const o=e.useMemo(()=>{const e={style:{},transform:{},transformOrigin:{},vars:{},attrs:{}};return Gs(e,n,qs(s),t.transformTemplate),{...e.attrs,style:{...e.style}}},[n]);if(t.style){const e={};Dl(e,t.style,t),o.style={...e,...o.style}}return o}function Fl(t=!1){return(n,i,s,{latestValues:o},r)=>{const a=(Al(n)?Bl:Ll)(i,o,r,n),l=zi(i,"string"==typeof n,t),u=n!==e.Fragment?{...l,...a,ref:s}:{},{children:c}=i,h=e.useMemo(()=>Mt(c)?c.get():c,[c]);return e.createElement(n,{...u,children:h})}}function jl(t,e){return function(n,{forwardMotionProps:i}={forwardMotionProps:!1}){return Tl({...Al(n)?Vl:Rl,preloadedFeatures:t,useRender:Fl(i),createVisualElement:e,Component:n})}}const Ol=(t,n)=>Al(t)?new so(n):new wo(n,{allowProjection:t!==e.Fragment}),Il=Oa(jl({...Vi,...fl,...rl,...gl},Ol));function Ul({children:t,as:n="ul",axis:i="y",onReorder:s,values:o,...r},a){const l=Xi(()=>Il[n]),u=[],c=e.useRef(!1),h={axis:i,registerItem:(t,e)=>{const n=u.findIndex(e=>t===e.value);-1!==n?u[n].layout=e[i]:u.push({value:t,layout:e[i]}),u.sort(zl)},updateOrder:(t,e,n)=>{if(c.current)return;const i=function(t,e,n,i){if(!i)return t;const s=t.findIndex(t=>t.value===e);if(-1===s)return t;const o=i>0?1:-1,r=t[s+o];if(!r)return t;const a=t[s],l=r.layout,u=an(l.min,l.max,.5);return 1===o&&a.layout.max+n>u||-1===o&&a.layout.min+n<u?function([...t],e,n){const i=e<0?t.length+e:e;if(i>=0&&i<t.length){const i=n<0?t.length+n:n,[s]=t.splice(e,1);t.splice(i,0,s)}return t}(t,s,s+o):t}(u,t,e,n);u!==i&&(c.current=!0,s(i.map(Nl).filter(t=>-1!==o.indexOf(t))))}};return e.useEffect(()=>{c.current=!1}),d(l,{...r,ref:a,ignoreStrict:!0,children:d(ja.Provider,{value:h,children:t})})}const Wl=e.forwardRef(Ul);function Nl(t){return t.value}function zl(t,e){return t.layout.min-e.layout.min}function $l(t){const n=Xi(()=>bt(t)),{isStatic:i}=e.useContext(ss);if(i){const[,i]=e.useState(t);e.useEffect(()=>n.on("change",i),[])}return n}function Hl(t,e){const n=$l(e()),i=()=>n.set(e());return i(),ns(()=>{const e=()=>lt.preRender(i,!1,!0),n=t.map(t=>t.on("change",e));return()=>{n.forEach(t=>t()),ut(i)}}),n}function Yl(t,e,n,i){if("function"==typeof t)return function(t){Tt.current=[],t();const e=Hl(Tt.current,t);return Tt.current=void 0,e}(t);const s="function"==typeof e?e:fr(e,n,i);return Array.isArray(t)?Xl(t,s):Xl([t],([t])=>s(t))}function Xl(t,e){const n=Xi(()=>[]);return Hl(t,()=>{n.length=0;const i=t.length;for(let e=0;e<i;e++)n[e]=t[e].get();return e(n)})}function Kl(t,e=0){return Mt(t)?t:$l(e)}function Gl({children:t,style:n={},value:i,as:s="li",onDrag:o,layout:r=!0,...a},l){const u=Xi(()=>Il[s]),c=e.useContext(ja),h={x:Kl(n.x),y:Kl(n.y)},p=Yl([h.x,h.y],([t,e])=>t||e?1:"unset"),{axis:m,registerItem:f,updateOrder:g}=c;return d(u,{drag:m,...a,dragSnapToOrigin:!0,style:{...n,x:h.x,y:h.y,zIndex:p},layout:r,onDrag:(t,e)=>{const{velocity:n}=e;n[m]&&g(i,h[m].get(),n[m]),o&&o(t,e)},onLayoutMeasure:t=>f(i,t),ref:l,ignoreStrict:!0,children:t})}const _l=e.forwardRef(Gl);var ql=Object.freeze({__proto__:null,Group:Wl,Item:_l});const Zl=Oa(jl()),Jl={renderer:Ol,...Vi,...fl},Ql={...Jl,...rl,...gl},tu={renderer:Ol,...Vi};function eu(t,n,i){e.useInsertionEffect(()=>t.on(n,i),[t,n,i])}function nu(t,e){A(Boolean(!e||e.current))}const iu=()=>({scrollX:bt(0),scrollY:bt(0),scrollXProgress:bt(0),scrollYProgress:bt(0)});function su({container:t,target:n,layoutEffect:i=!0,...s}={}){const o=Xi(iu);return(i?ns:e.useEffect)(()=>(nu(0,n),nu(0,t),ur((t,{x:e,y:n})=>{o.scrollX.set(e.current),o.scrollXProgress.set(e.progress),o.scrollY.set(n.current),o.scrollYProgress.set(n.progress)},{...s,container:(null==t?void 0:t.current)||void 0,target:(null==n?void 0:n.current)||void 0})),[t,n,JSON.stringify(s.offset)]),o}function ou(t){return"number"==typeof t?t:parseFloat(t)}function ru(t){const n=e.useRef(0),{isStatic:i}=e.useContext(ss);e.useEffect(()=>{if(i)return;const e=({timestamp:e,delta:i})=>{n.current||(n.current=e),t(e-n.current,i)};return lt.update(e,!0),()=>ut(e)},[t])}class au extends St{constructor(){super(...arguments),this.values=[]}add(t){const e=function(t){return pt.has(t)?"transform":oi.has(t)?Vt(t):void 0}(t);e&&(vt(this.values,e),this.update())}update(){this.set(this.values.length?this.values.join(", "):"auto")}}function lu(){!Bs.current&&Fs();const[t]=e.useState(Ls.current);return t}function uu(t,e){[...e].reverse().forEach(n=>{const i=t.getVariant(n);i&&Et(t,i),t.variantChildren&&t.variantChildren.forEach(t=>{uu(t,e)})})}function cu(){const t=new Set,e={subscribe:e=>(t.add(e),()=>{t.delete(e)}),start(e,n){const i=[];return t.forEach(t=>{i.push(xi(t,e,{transitionOverride:n}))}),Promise.all(i)},set:e=>t.forEach(t=>{!function(t,e){Array.isArray(e)?uu(t,e):"string"==typeof e?uu(t,[e]):Et(t,e)}(t,e)}),stop(){t.forEach(t=>{!function(t){t.values.forEach(t=>t.stop())}(t)})},mount:()=>()=>{e.stop()}};return e}function hu(){const t=Xi(cu);return ns(t.mount,[]),t}const du=hu;class pu{constructor(){this.componentControls=new Set}subscribe(t){return this.componentControls.add(t),()=>this.componentControls.delete(t)}start(t,e){this.componentControls.forEach(n=>{n.start(t.nativeEvent||t,e)})}}const mu=()=>new pu;function fu(t){return null!==t&&"object"==typeof t&&xl in t}function gu(){return yu}function yu(t){ba.current&&(ba.current.isUpdating=!1,ba.current.blockUpdate(),t&&t())}const vu=(t,e)=>`${t}: ${pt.has(e)?"transform":e}`,xu=new Map,wu=new Map;function Pu(t,e,n){var i;const s=vu(t,e),o=xu.get(s);if(!o)return null;const{animation:r,startTime:a}=o;function l(){var i;null===(i=window.MotionCancelOptimisedAnimation)||void 0===i||i.call(window,t,e,n)}return r.onfinish=l,null===a||(null===(i=window.MotionHandoffIsComplete)||void 0===i?void 0:i.call(window,t))?(l(),null):a}let Tu,Su;const bu=new Set;function Au(){bu.forEach(t=>{t.animation.play(),t.animation.startTime=t.startTime}),bu.clear()}const Eu=()=>({});class Mu extends Is{constructor(){super(...arguments),this.measureInstanceViewportBox=Di}build(){}resetTransform(){}restoreTransform(){}removeValueFromRenderState(){}renderInstance(){}scrapeMotionValuesFromProps(){return{}}getBaseTargetFromProps(){}readValueFromInstance(t,e,n){return n.initialState[e]||0}sortInstanceNodePosition(){return 0}}const Cu=Gi({scrapeMotionValuesFromProps:Eu,createRenderState:Eu});let Vu=0;const Ru=t=>t>.001?1/t:1e5;t.AcceleratedAnimation=ui,t.AnimatePresence=({children:t,custom:n,initial:i=!0,onExitComplete:s,presenceAffectsLayout:o=!0,mode:r="sync",propagate:a=!1})=>{const[l,u]=us(a),c=e.useMemo(()=>hs(t),[t]),p=a&&!l?[]:c.map(cs),f=e.useRef(!0),g=e.useRef(c),y=Xi(()=>new Map),[v,x]=e.useState(c),[w,P]=e.useState(c);ns(()=>{f.current=!1,g.current=c;for(let t=0;t<w.length;t++){const e=cs(w[t]);p.includes(e)?y.delete(e):!0!==y.get(e)&&y.set(e,!1)}},[w,p.length,p.join("-")]);const T=[];if(c!==v){let t=[...c];for(let e=0;e<w.length;e++){const n=w[e],i=cs(n);p.includes(i)||(t.splice(e,0,n),T.push(n))}return"wait"===r&&T.length&&(t=T),P(hs(t)),void x(c)}const{forceRender:S}=e.useContext(m);return d(h,{children:w.map(t=>{const e=cs(t),h=!(a&&!l)&&(c===w||p.includes(e));return d(as,{isPresent:h,initial:!(f.current&&!i)&&void 0,custom:h?void 0:n,presenceAffectsLayout:o,mode:r,onExitComplete:h?void 0:()=>{if(!y.has(e))return;y.set(e,!0);let t=!0;y.forEach(e=>{e||(t=!1)}),t&&(null==S||S(),P(g.current),a&&(null==u||u()),s&&s())},children:t},e)})})},t.AnimateSharedLayout=({children:t})=>(i.useEffect(()=>{},[]),d(ka,{id:Xi(()=>"asl-"+Vu++),children:t})),t.DeprecatedLayoutGroupContext=ds,t.DragControls=pu,t.FlatTree=Pr,t.LayoutGroup=ka,t.LayoutGroupContext=m,t.LazyMotion=function({children:t,features:n,strict:i=!1}){const[,s]=e.useState(!Fa(n)),o=e.useRef(void 0);if(!Fa(n)){const{renderer:t,...e}=n;o.current=t,Ba(e)}return e.useEffect(()=>{Fa(n)&&n().then(({renderer:t,...e})=>{Ba(e),o.current=t,s(!0)})},[]),d(La.Provider,{value:{renderer:o.current,strict:i},children:t})},t.MotionConfig=function({children:t,isValidProp:n,...i}){n&&Ni(n),(i={...e.useContext(ss),...i}).isStatic=Xi(()=>i.isStatic);const s=e.useMemo(()=>i,[JSON.stringify(i.transition),i.transformPagePoint,i.reducedMotion]);return d(ss.Provider,{value:s,children:t})},t.MotionConfigContext=ss,t.MotionContext=Ri,t.MotionGlobalConfig=ot,t.MotionValue=St,t.PresenceContext=$i,t.Reorder=ql,t.SwitchLayoutGroupContext=nl,t.VisualElement=Is,t.addPointerEvent=Qi,t.addPointerInfo=Ji,t.addScaleCorrector=to,t.animate=Mo,t.animateMini=Fo,t.animateValue=si,t.animateVisualElement=xi,t.animationControls=cu,t.animations=Vi,t.anticipate=Wt,t.backIn=It,t.backInOut=Ut,t.backOut=Ot,t.buildTransform=$s,t.calcLength=ki,t.cancelFrame=ut,t.cancelSync=yr,t.circIn=Nt,t.circInOut=$t,t.circOut=zt,t.clamp=Yt,t.color=he,t.complex=ve,t.createBox=Di,t.createRendererMotionComponent=Tl,t.createScopedAnimate=Eo,t.cubicBezier=Bt,t.delay=dr,t.disableInstantTransitions=function(){kt.current=!1},t.distance=pr,t.distance2D=mr,t.domAnimation=Jl,t.domMax=Ql,t.domMin=tu,t.easeIn=Hn,t.easeInOut=Xn,t.easeOut=Yn,t.filterProps=zi,t.findSpring=On,t.frame=lt,t.frameData=ct,t.frameSteps=ht,t.inView=hr,t.inertia=$n,t.interpolate=qn,t.invariant=E,t.isBrowser=ts,t.isDragActive=K,t.isMotionComponent=fu,t.isMotionValue=Mt,t.isValidMotionProp=Ui,t.keyframes=Qn,t.m=Zl,t.makeUseVisualState=Gi,t.mirrorEasing=Ft,t.mix=Tn,t.motion=Il,t.motionValue=bt,t.noop=b,t.optimizedAppearDataAttribute=Rt,t.pipe=fn,t.progress=C,t.px=re,t.resolveMotionValue=Ki,t.reverseEasing=jt,t.scroll=ur,t.scrollInfo=or,t.spring=zn,t.stagger=function(t=.1,{startDelay:e=0,from:n=0,ease:i}={}){return(s,o)=>{const r="number"==typeof n?n:function(t,e){if("first"===t)return 0;{const n=e-1;return"last"===t?n:n/2}}(n,o),a=Math.abs(r-s);let l=t*a;if(i){const e=o*t;l=_n(i)(l/e)*e}return e+l}},t.startOptimizedAppearAnimation=function(t,e,n,i,s){if(window.MotionIsMounted)return;const o=t.dataset.framerAppearId;if(!o)return;window.MotionHandoffAnimation=Pu;const r=vu(o,e);Su||(Su=ri(t,e,[n[0],n[0]],{duration:1e4,ease:"linear"}),xu.set(r,{animation:Su,startTime:null}),window.MotionHandoffAnimation=Pu,window.MotionHasOptimisedAnimation=(t,e)=>{if(!t)return!1;if(!e)return wu.has(t);const n=vu(t,e);return Boolean(xu.get(n))},window.MotionHandoffMarkAsComplete=t=>{wu.has(t)&&wu.set(t,!0)},window.MotionHandoffIsComplete=t=>!0===wu.get(t),window.MotionCancelOptimisedAnimation=(t,e,n,i)=>{const s=vu(t,e),o=xu.get(s);o&&(n&&void 0===i?n.postRender(()=>{n.postRender(()=>{o.animation.cancel()})}):o.animation.cancel(),n&&i?(bu.add(o),n.render(Au)):(xu.delete(s),xu.size||(window.MotionCancelOptimisedAnimation=void 0)))},window.MotionCheckAppearSync=(t,e,n)=>{var i,s;const o=Dt(t);if(!o)return;const r=null===(i=window.MotionHasOptimisedAnimation)||void 0===i?void 0:i.call(window,o,e),a=null===(s=t.props.values)||void 0===s?void 0:s[e];if(!r||!a)return;const l=n.on("change",t=>{var n;a.get()!==t&&(null===(n=window.MotionCancelOptimisedAnimation)||void 0===n||n.call(window,o,e),l())});return l});const a=()=>{Su.cancel();const o=ri(t,e,n,i);void 0===Tu&&(Tu=performance.now()),o.startTime=Tu,xu.set(r,{animation:o,startTime:Tu}),s&&s(o)};wu.set(o,!1),Su.ready?Su.ready.then(a).catch(b):a()},t.steps=function(t,e="end"){return n=>{const i=(n="end"===e?Math.min(n,.999):Math.max(n,.001))*t,s="end"===e?Math.floor(i):Math.ceil(i);return Yt(0,1,s/t)}},t.sync=gr,t.time=yt,t.transform=fr,t.unwrapMotionComponent=function(t){if(fu(t))return t[xl]},t.useAnimate=function(){const t=Xi(()=>({current:null,animations:[]})),e=Xi(()=>Eo(t));return es(()=>{t.animations.forEach(t=>t.stop())}),[t,e]},t.useAnimateMini=function(){const t=Xi(()=>({current:null,animations:[]})),e=Xi(()=>Bo(t));return es(()=>{t.animations.forEach(t=>t.stop())}),[t,e]},t.useAnimation=du,t.useAnimationControls=hu,t.useAnimationFrame=ru,t.useCycle=function(...t){const n=e.useRef(0),[i,s]=e.useState(t[n.current]);return[i,e.useCallback(e=>{n.current="number"!=typeof e?ms(0,t.length,n.current+1):e,s(t[n.current])},[t.length,...t])]},t.useDeprecatedAnimatedState=function(t){const[n,i]=e.useState(t),s=Cu({},!1),o=Xi(()=>new Mu({props:{onUpdate:t=>{i({...t})}},visualState:s,presenceContext:null},{initialState:t}));return e.useLayoutEffect(()=>(o.mount({}),()=>o.unmount()),[o]),[n,Xi(()=>t=>xi(o,t))]},t.useDeprecatedInvertedScale=function(t){let n=$l(1),i=$l(1);const{visualElement:s}=e.useContext(Ri);return t?(n=t.scaleX||n,i=t.scaleY||i):s&&(n=s.getValue("scaleX",1),i=s.getValue("scaleY",1)),{scaleX:Yl(n,Ru),scaleY:Yl(i,Ru)}},t.useDomEvent=function(t,n,i,s){e.useEffect(()=>{const e=t.current;if(i&&e)return qi(e,n,i,s)},[t,n,i,s])},t.useDragControls=function(){return Xi(mu)},t.useElementScroll=function(t){return su({container:t})},t.useForceUpdate=is,t.useInView=function(t,{root:n,margin:i,amount:s,once:o=!1}={}){const[r,a]=e.useState(!1);return e.useEffect(()=>{if(!t.current||o&&r)return;const e={root:n&&n.current||void 0,margin:i,amount:s};return hr(t.current,()=>(a(!0),o?void 0:()=>a(!1)),e)},[n,t,i,o,s]),r},t.useInstantLayoutTransition=gu,t.useInstantTransition=function(){const[t,n]=is(),i=gu(),s=e.useRef(-1);return e.useEffect(()=>{lt.postRender(()=>lt.postRender(()=>{n===s.current&&(kt.current=!1)}))},[n]),e=>{i(()=>{kt.current=!0,t(),e(),s.current=n+1})}},t.useIsPresent=function(){return null===(t=e.useContext($i))||t.isPresent;var t},t.useIsomorphicLayoutEffect=ns,t.useMotionTemplate=function(t,...e){const n=t.length;return Hl(e.filter(Mt),(function(){let i="";for(let s=0;s<n;s++){i+=t[s];const n=e[s];n&&(i+=Mt(n)?n.get():n)}return i}))},t.useMotionValue=$l,t.useMotionValueEvent=eu,t.usePresence=us,t.useReducedMotion=lu,t.useReducedMotionConfig=function(){const t=lu(),{reducedMotion:n}=e.useContext(ss);return"never"!==n&&("always"===n||t)},t.useResetProjection=function(){return e.useCallback(()=>{const t=ba.current;t&&t.resetTree()},[])},t.useScroll=su,t.useSpring=function(t,n={}){const{isStatic:i}=e.useContext(ss),s=e.useRef(null),o=$l(Mt(t)?ou(t.get()):t),r=e.useRef(o.get()),a=e.useRef(()=>{}),l=()=>{const t=s.current;t&&0===t.time&&t.sample(ct.delta),u(),s.current=si({keyframes:[o.get(),r.current],velocity:o.getVelocity(),type:"spring",restDelta:.001,restSpeed:.01,...n,onUpdate:a.current})},u=()=>{s.current&&s.current.stop()};return e.useInsertionEffect(()=>o.attach((t,e)=>i?e(t):(r.current=t,a.current=e,lt.update(l),o.get()),u),[JSON.stringify(n)]),ns(()=>{if(Mt(t))return t.on("change",t=>o.set(ou(t)))},[o]),o},t.useTime=function(){const t=$l(0);return ru(e=>t.set(e)),t},t.useTransform=Yl,t.useUnmountEffect=es,t.useVelocity=function(t){const e=$l(t.getVelocity()),n=()=>{const i=t.getVelocity();e.set(i),i&&lt.update(n)};return eu(t,"change",()=>{lt.update(n,!1,!0)}),e},t.useViewportScroll=function(){return su()},t.useWillChange=function(){return Xi(()=>new au("auto"))},t.visualElementStore=Vs,t.wrap=ms}));
