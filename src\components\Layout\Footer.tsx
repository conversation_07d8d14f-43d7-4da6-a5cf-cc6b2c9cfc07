'use client'

import { motion } from 'framer-motion'

export default function Footer() {
  return (
    <motion.footer 
      className="bg-gray-800 text-white py-8 mt-16"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ delay: 0.5, duration: 0.5 }}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
          {/* Support Section */}
          <div className="text-center md:text-left">
            <h3 className="text-lg font-semibold mb-2">
              Support your Snow Day Calculator!
            </h3>
            <div className="flex items-center space-x-4">
              <button className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded transition-colors">
                Follow @SnowDayCalc
              </button>
              <button className="bg-green-600 hover:bg-green-700 px-4 py-2 rounded transition-colors">
                Donate
              </button>
            </div>
          </div>

          {/* Links */}
          <div className="flex items-center space-x-6 text-sm">
            <button className="hover:text-orange-400 transition-colors">
              Send Feedback
            </button>
            <span>•</span>
            <button className="hover:text-orange-400 transition-colors">
              Contact DAS
            </button>
          </div>
        </div>

        {/* Copyright */}
        <div className="mt-6 pt-6 border-t border-gray-700 text-center text-sm text-gray-400">
          © 2007-2025 David Sukhin. All rights reserved.
        </div>
      </div>
    </motion.footer>
  )
}
