import { NextRequest, NextResponse } from 'next/server'
import { weatherService } from '@/lib/weather'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const zipCode = searchParams.get('zipCode')

    if (!zipCode) {
      return NextResponse.json(
        { error: 'Zip code is required' },
        { status: 400 }
      )
    }

    // Validate zip code format
    const zipRegex = /^(\d{5}|[A-Za-z]\d[A-Za-z] \d[A-Za-z]\d)$/
    if (!zipRegex.test(zipCode)) {
      return NextResponse.json(
        { error: 'Invalid zip code format' },
        { status: 400 }
      )
    }

    let weatherData
    
    // Try to get real weather data, fallback to mock data if API key is not available
    if (process.env.WEATHER_API_KEY) {
      try {
        weatherData = await weatherService.getCurrentWeather(zipCode)
      } catch (error) {
        console.warn('Weather API failed, using mock data:', error)
        weatherData = await weatherService.getMockWeatherData(zipCode)
      }
    } else {
      console.warn('No weather API key found, using mock data')
      weatherData = await weatherService.getMockWeatherData(zipCode)
    }

    return NextResponse.json({
      success: true,
      data: weatherData,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Weather API error:', error)
    return NextResponse.json(
      { 
        error: 'Failed to fetch weather data',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { zipCode, manualData } = body

    if (!zipCode) {
      return NextResponse.json(
        { error: 'Zip code is required' },
        { status: 400 }
      )
    }

    // If manual data is provided, use it instead of API data
    if (manualData) {
      const mockForecast = [
        {
          date: new Date(Date.now() + 86400000).toISOString().split('T')[0],
          dayOfWeek: 'Tomorrow',
          tempHigh: manualData.temperature + 5,
          tempLow: manualData.temperature - 5,
          precipitation: manualData.snowfall * 0.1, // Convert snow to precipitation
          snowfall: manualData.snowfall,
          windSpeed: manualData.windSpeed,
          conditions: manualData.snowfall > 0 ? 'Snow' : 'Clear',
          precipitationChance: manualData.snowfall > 0 ? 80 : 20
        }
      ]

      const weatherData = {
        temperature: manualData.temperature,
        humidity: 70,
        windSpeed: manualData.windSpeed,
        windDirection: 270,
        precipitation: manualData.snowfall * 0.1,
        snowfall: manualData.snowfall,
        visibility: manualData.snowfall > 2 ? 1 : 10,
        pressure: 1013,
        cloudCover: manualData.snowfall > 0 ? 90 : 30,
        conditions: manualData.snowfall > 0 ? 'Snow' : 'Clear',
        forecast: mockForecast
      }

      return NextResponse.json({
        success: true,
        data: weatherData,
        manual: true,
        timestamp: new Date().toISOString()
      })
    }

    // Otherwise, get automatic weather data
    let weatherData
    if (process.env.WEATHER_API_KEY) {
      try {
        weatherData = await weatherService.getCurrentWeather(zipCode)
      } catch (error) {
        weatherData = await weatherService.getMockWeatherData(zipCode)
      }
    } else {
      weatherData = await weatherService.getMockWeatherData(zipCode)
    }

    return NextResponse.json({
      success: true,
      data: weatherData,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Weather API POST error:', error)
    return NextResponse.json(
      { 
        error: 'Failed to process weather request',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
