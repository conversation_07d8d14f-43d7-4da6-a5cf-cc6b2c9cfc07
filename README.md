# Snow Day Calculator

A modern recreation of the popular Snow Day Calculator website, built with Next.js, TypeScript, and Tailwind CSS.

## Features

- **Automatic Weather Prediction**: Enter your zip code to get automatic snow day predictions based on real weather data
- **Manual Weather Input**: Input weather conditions manually for more precise predictions
- **Multi-day Forecasts**: Get predictions for the next 2 school days
- **School Type Support**: Customized predictions for different school types (Public, Private, Rural, etc.)
- **Social Sharing**: Share your predictions on Facebook and Twitter
- **Responsive Design**: Optimized for desktop, tablet, and mobile devices
- **Smooth Animations**: Beautiful page transitions and interactive elements
- **Contact System**: Built-in feedback and contact form

## Technology Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **Animations**: Framer Motion
- **Weather API**: OpenWeatherMap (with fallback to mock data)
- **Email**: Nodemailer for contact form
- **Icons**: Lucide React

## Getting Started

### Prerequisites

- Node.js 18+ 
- npm or yarn
- OpenWeatherMap API key (optional - will use mock data if not provided)

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd snow-day-calculator
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.local.example .env.local
```

Edit `.env.local` and add your API keys:
```env
# Weather API (optional)
WEATHER_API_KEY=your_openweathermap_api_key

# Email configuration (optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
```

4. Run the development server:
```bash
npm run dev
```

5. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── api/               # API routes
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Home page
├── components/            # React components
│   ├── Calculator/        # Calculator form components
│   ├── Contact/          # Contact form
│   ├── Layout/           # Layout components
│   ├── Results/          # Results display
│   └── UI/               # Reusable UI components
└── lib/                  # Utility libraries
    ├── prediction.ts     # Snow day prediction algorithm
    ├── social.ts         # Social sharing utilities
    └── weather.ts        # Weather API integration
```

## API Endpoints

- `GET/POST /api/weather` - Weather data retrieval
- `POST /api/predict` - Snow day prediction calculation
- `POST /api/contact` - Contact form submission
- `GET /api/predict` - Share data generation

## Prediction Algorithm

The snow day prediction algorithm considers multiple factors:

- **Temperature**: Optimal range for snow accumulation
- **Snowfall Amount**: Primary factor for school closures
- **Wind Speed**: Affects drifting and visibility
- **Storm Timing**: Overnight storms have higher closure probability
- **School Type**: Different closure thresholds for different school types
- **Snow Days Used**: Schools with more used snow days are less likely to close
- **Ice Conditions**: Significantly increases closure probability

## Deployment

### Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy

### Other Platforms

The app can be deployed to any platform that supports Next.js:

- Netlify
- Railway
- DigitalOcean App Platform
- AWS Amplify

## Configuration

### Weather API

The app uses OpenWeatherMap API for weather data. If no API key is provided, it will use mock data for demonstration purposes.

To get an API key:
1. Sign up at [OpenWeatherMap](https://openweathermap.org/api)
2. Get your free API key
3. Add it to your `.env.local` file

### Email Configuration

For the contact form to work, configure SMTP settings in your `.env.local` file. Gmail with App Passwords is recommended for development.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is for educational purposes. The original Snow Day Calculator is owned by David Sukhin.

## Acknowledgments

- Original Snow Day Calculator by David Sukhin
- Weather data provided by OpenWeatherMap
- Icons by Lucide
- Animations powered by Framer Motion
