import { WeatherData, ForecastDay } from './weather'
import { FormData } from '@/components/Calculator/CalculatorForm'
import { ManualWeatherData } from '@/components/Calculator/ManualInput'

export interface PredictionResult {
  date: string
  dayOfWeek: string
  percentage: number
  message: string
  factors: PredictionFactors
  shareData: ShareData
}

export interface PredictionFactors {
  temperature: number
  snowfall: number
  windSpeed: number
  timing: number
  schoolType: number
  snowDaysUsed: number
  extraFactors: number
  total: number
}

export interface ShareData {
  url: string
  text: string
  hashtags: string[]
}

class SnowDayPredictor {
  // School type multipliers
  private schoolTypeMultipliers = {
    'public': 1.0,
    'urban-public': 0.8,
    'rural-public': 1.2,
    'private': 0.7,
    'boarding': 0.3
  }

  // Base prediction algorithm based on original Snow Day Calculator logic
  calculateSnowDayChance(
    weatherData: WeatherData,
    formData: FormData,
    forecastDay: ForecastDay,
    manualData?: ManualWeatherData
  ): PredictionResult {
    const factors: PredictionFactors = {
      temperature: 0,
      snowfall: 0,
      windSpeed: 0,
      timing: 0,
      schoolType: 0,
      snowDaysUsed: 0,
      extraFactors: 0,
      total: 0
    }

    // Use manual data if provided, otherwise use forecast data
    const temp = manualData?.temperature ?? forecastDay.tempLow
    const snow = manualData?.snowfall ?? forecastDay.snowfall
    const wind = manualData?.windSpeed ?? forecastDay.windSpeed
    const precipChance = forecastDay.precipitationChance

    // Temperature factor (optimal range: 15-32°F)
    factors.temperature = this.calculateTemperatureFactor(temp)

    // Snowfall factor (most important factor)
    factors.snowfall = this.calculateSnowfallFactor(snow, precipChance)

    // Wind factor (higher wind = more drifting = higher chance)
    factors.windSpeed = this.calculateWindFactor(wind)

    // Timing factor (overnight storms are best)
    factors.timing = this.calculateTimingFactor(manualData?.stormTiming)

    // School type factor
    factors.schoolType = this.calculateSchoolTypeFactor(formData.schoolType)

    // Snow days already used factor (more used = less likely to close)
    factors.snowDaysUsed = this.calculateSnowDaysUsedFactor(formData.snowDays)

    // Extra factors from user input
    factors.extraFactors = formData.extraFactors * -5 // Negative because lower is better

    // Ice conditions bonus (if manual data)
    if (manualData?.iceConditions) {
      factors.extraFactors += 15
    }

    // Calculate total score
    factors.total = 
      factors.temperature +
      factors.snowfall +
      factors.windSpeed +
      factors.timing +
      factors.schoolType +
      factors.snowDaysUsed +
      factors.extraFactors

    // Convert score to percentage (0-100)
    let percentage = Math.max(0, Math.min(100, factors.total))

    // Apply some randomness to make it more realistic
    percentage = Math.round(percentage + (Math.random() - 0.5) * 5)

    // Generate message based on percentage
    const message = this.generateMessage(percentage, snow, temp)

    // Generate share data
    const shareData = this.generateShareData(formData.zipCode, percentage, forecastDay.dayOfWeek)

    return {
      date: forecastDay.date,
      dayOfWeek: forecastDay.dayOfWeek,
      percentage: Math.max(0, Math.min(99, percentage)), // Cap at 99% like original
      message,
      factors,
      shareData
    }
  }

  private calculateTemperatureFactor(temp: number): number {
    if (temp > 35) return 0 // Too warm for snow to stick
    if (temp < 10) return 5 // Very cold, but may be too cold for heavy snow
    if (temp >= 25 && temp <= 32) return 20 // Optimal range
    if (temp >= 15 && temp < 25) return 15 // Good range
    return 10 // Marginal
  }

  private calculateSnowfallFactor(snowfall: number, precipChance: number): number {
    let factor = 0
    
    // Base snowfall factor
    if (snowfall >= 6) factor = 40
    else if (snowfall >= 4) factor = 35
    else if (snowfall >= 2) factor = 25
    else if (snowfall >= 1) factor = 15
    else if (snowfall >= 0.5) factor = 8
    else factor = 0

    // Adjust for precipitation chance
    factor *= (precipChance / 100)

    return Math.round(factor)
  }

  private calculateWindFactor(windSpeed: number): number {
    if (windSpeed >= 25) return 10 // High winds create drifting
    if (windSpeed >= 15) return 5
    return 0
  }

  private calculateTimingFactor(timing?: string): number {
    switch (timing) {
      case 'overnight': return 15 // Best timing
      case 'before-school': return 10
      case 'during-school': return 5
      case 'after-school': return 0
      default: return 8 // Default assumption for overnight
    }
  }

  private calculateSchoolTypeFactor(schoolType: string): number {
    const multiplier = this.schoolTypeMultipliers[schoolType as keyof typeof this.schoolTypeMultipliers] || 1.0
    return Math.round((multiplier - 1.0) * 20) // Convert to additive factor
  }

  private calculateSnowDaysUsedFactor(snowDaysUsed: number): number {
    // More snow days used = less likely to close
    return Math.max(-15, -snowDaysUsed * 2)
  }

  private generateMessage(percentage: number, snowfall: number, temperature: number): string {
    if (percentage === 0 || snowfall === 0) {
      return "No Snow Storm. Sorry!"
    }
    
    if (percentage < 20) {
      return "Little to no chance of anything, but possible."
    } else if (percentage < 55) {
      return "Low chance, but keep your fingers crossed!"
    } else if (percentage < 75) {
      return "Delay Likely."
    } else if (percentage < 87) {
      return "Possibility of No School."
    } else {
      return "No School or Possible Early Dismissal."
    }
  }

  private generateShareData(zipCode: string, percentage: number, dayOfWeek: string): ShareData {
    const text = `The @SnowDayCalc says there is a ${percentage}% chance of a #SnowDay this ${dayOfWeek} for ${zipCode}!`
    const url = `${window.location.origin}/prediction?zipcode=${zipCode}&prediction=${percentage}`
    
    return {
      url,
      text,
      hashtags: ['SnowDay', 'Weather', 'School']
    }
  }

  // Calculate predictions for multiple days
  calculateMultiDayPredictions(
    weatherData: WeatherData,
    formData: FormData,
    manualData?: ManualWeatherData
  ): PredictionResult[] {
    const predictions: PredictionResult[] = []

    // If manual data is provided, create a single prediction for tomorrow
    if (manualData) {
      const tomorrow = new Date()
      tomorrow.setDate(tomorrow.getDate() + 1)
      
      const mockForecastDay: ForecastDay = {
        date: tomorrow.toISOString().split('T')[0],
        dayOfWeek: tomorrow.toLocaleDateString('en-US', { weekday: 'long' }),
        tempHigh: manualData.temperature + 5,
        tempLow: manualData.temperature,
        precipitation: manualData.snowfall * 0.1,
        snowfall: manualData.snowfall,
        windSpeed: manualData.windSpeed,
        conditions: manualData.snowfall > 0 ? 'Snow' : 'Clear',
        precipitationChance: manualData.snowfall > 0 ? 80 : 20
      }

      predictions.push(this.calculateSnowDayChance(weatherData, formData, mockForecastDay, manualData))
    } else {
      // Use forecast data for automatic predictions
      weatherData.forecast.forEach(forecastDay => {
        // Skip weekends (Saturday = 6, Sunday = 0)
        const date = new Date(forecastDay.date)
        const dayOfWeek = date.getDay()
        
        if (dayOfWeek !== 0 && dayOfWeek !== 6) { // Not Sunday or Saturday
          predictions.push(this.calculateSnowDayChance(weatherData, formData, forecastDay))
        }
      })
    }

    return predictions
  }
}

export const snowDayPredictor = new SnowDayPredictor()
