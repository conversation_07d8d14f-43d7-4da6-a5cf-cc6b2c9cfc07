'use client'

import { motion } from 'framer-motion'
import { PredictionResult } from '@/lib/prediction'
import { WeatherData } from '@/lib/weather'
import ShareButtons from './ShareButtons'
import { Calendar, Thermometer, Cloud, Wind, Clock } from 'lucide-react'

interface PredictionDisplayProps {
  predictions: PredictionResult[]
  weatherData: WeatherData
  zipCode: string
  onNewPrediction: () => void
}

export default function PredictionDisplay({ 
  predictions, 
  weatherData, 
  zipCode, 
  onNewPrediction 
}: PredictionDisplayProps) {
  const getPercentageColor = (percentage: number) => {
    if (percentage >= 87) return 'text-red-600'
    if (percentage >= 75) return 'text-orange-600'
    if (percentage >= 55) return 'text-yellow-600'
    if (percentage >= 20) return 'text-blue-600'
    return 'text-gray-600'
  }

  const getPercentageBackground = (percentage: number) => {
    if (percentage >= 87) return 'bg-red-50 border-red-200'
    if (percentage >= 75) return 'bg-orange-50 border-orange-200'
    if (percentage >= 55) return 'bg-yellow-50 border-yellow-200'
    if (percentage >= 20) return 'bg-blue-50 border-blue-200'
    return 'bg-gray-50 border-gray-200'
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className="p-8"
    >
      {/* Header */}
      <div className="text-center mb-8">
        <motion.h2 
          className="text-3xl font-century-gothic font-bold text-gray-800 mb-2"
          initial={{ scale: 0.9 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.2 }}
        >
          Prediction for {zipCode}
        </motion.h2>
        <p className="text-gray-600">
          Data current as of: {new Date().toLocaleString()}
        </p>
      </div>

      {/* Predictions */}
      <div className="space-y-6 mb-8">
        {predictions.length === 0 ? (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center py-8"
          >
            <p className="text-xl text-gray-600 mb-4">
              N/A - Enjoy the Weekend
            </p>
            <p className="text-gray-500">
              The calculator does not predict weekend snowdays by default.
            </p>
          </motion.div>
        ) : (
          predictions.map((prediction, index) => (
            <motion.div
              key={prediction.date}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 + 0.3 }}
              className={`prediction-card ${getPercentageBackground(prediction.percentage)}`}
            >
              <div className="flex flex-col md:flex-row items-center justify-between">
                {/* Date and Day */}
                <div className="flex items-center space-x-3 mb-4 md:mb-0">
                  <Calendar className="w-6 h-6 text-gray-600" />
                  <div>
                    <h3 className="text-lg font-semibold text-gray-800">
                      {prediction.dayOfWeek}
                    </h3>
                    <p className="text-sm text-gray-600">
                      {new Date(prediction.date).toLocaleDateString()}
                    </p>
                  </div>
                </div>

                {/* Percentage */}
                <div className="text-center mb-4 md:mb-0">
                  <div className={`percentage-display ${getPercentageColor(prediction.percentage)}`}>
                    {prediction.percentage}%
                  </div>
                  <p className="text-sm font-medium text-gray-700">
                    Chance
                  </p>
                </div>

                {/* Message */}
                <div className="text-center md:text-right flex-1 md:ml-6">
                  <p className="text-lg font-medium text-gray-800 mb-2">
                    {prediction.message}
                  </p>
                  <ShareButtons prediction={prediction} />
                </div>
              </div>

              {/* Detailed Factors (Expandable) */}
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: 'auto', opacity: 1 }}
                transition={{ delay: index * 0.1 + 0.5 }}
                className="mt-6 pt-6 border-t border-gray-200"
              >
                <h4 className="text-sm font-semibold text-gray-700 mb-3">
                  Prediction Factors:
                </h4>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div className="flex items-center space-x-2">
                    <Thermometer className="w-4 h-4 text-blue-500" />
                    <span>Temp: +{prediction.factors.temperature}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Cloud className="w-4 h-4 text-gray-500" />
                    <span>Snow: +{prediction.factors.snowfall}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Wind className="w-4 h-4 text-green-500" />
                    <span>Wind: +{prediction.factors.windSpeed}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Clock className="w-4 h-4 text-purple-500" />
                    <span>Timing: +{prediction.factors.timing}</span>
                  </div>
                </div>
                <div className="mt-2 text-xs text-gray-600">
                  Total Score: {prediction.factors.total} → {prediction.percentage}% chance
                </div>
              </motion.div>
            </motion.div>
          ))
        )}
      </div>

      {/* Current Weather Info */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.8 }}
        className="bg-blue-50 rounded-lg p-6 border border-blue-200 mb-6"
      >
        <h3 className="text-lg font-semibold text-blue-800 mb-4">
          Current Weather Conditions
        </h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <span className="font-medium">Temperature:</span>
            <br />
            {weatherData.temperature}°F
          </div>
          <div>
            <span className="font-medium">Conditions:</span>
            <br />
            {weatherData.conditions}
          </div>
          <div>
            <span className="font-medium">Wind:</span>
            <br />
            {weatherData.windSpeed} mph
          </div>
          <div>
            <span className="font-medium">Humidity:</span>
            <br />
            {weatherData.humidity}%
          </div>
        </div>
      </motion.div>

      {/* Prediction Scale */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 1.0 }}
        className="bg-gray-50 rounded-lg p-6 border border-gray-200 mb-6"
      >
        <h3 className="text-lg font-semibold text-gray-800 mb-4">
          Prediction Scale
        </h3>
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="font-medium">Limited</span>
            <span>No chance due to low precipitation chance.</span>
          </div>
          <div className="flex justify-between">
            <span className="font-medium">0%-55%</span>
            <span>Little to no chance of anything, but possible.</span>
          </div>
          <div className="flex justify-between">
            <span className="font-medium">55%-75%</span>
            <span>Delay Likely.</span>
          </div>
          <div className="flex justify-between">
            <span className="font-medium">75%-87%</span>
            <span>Possibility of No School.</span>
          </div>
          <div className="flex justify-between">
            <span className="font-medium">87%-99%</span>
            <span>No School or Possible Early Dismissal.</span>
          </div>
        </div>
      </motion.div>

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-4 justify-center">
        <motion.button
          onClick={onNewPrediction}
          className="btn-primary"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          New Prediction
        </motion.button>
        <motion.button
          onClick={() => window.location.reload()}
          className="btn-secondary"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          Refresh Prediction
        </motion.button>
      </div>
    </motion.div>
  )
}
