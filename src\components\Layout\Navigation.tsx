'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'

interface NavigationProps {
  activeSection: string
  onSectionChange: (section: string) => void
}

const navigationItems = [
  { id: 'calculator', label: 'Calculator' },
  { id: 'about', label: 'About' },
  { id: 'apps', label: 'Apps' },
  { id: 'services', label: 'Services' },
  { id: 'contact', label: 'Contact' },
]

export default function Navigation({ activeSection, onSectionChange }: NavigationProps) {
  const [hoveredItem, setHoveredItem] = useState<string | null>(null)

  return (
    <nav className="fixed left-0 top-0 h-screen flex flex-col justify-center items-start pl-4 z-50">
      <div className="flex flex-col space-y-2">
        {navigationItems.map((item, index) => (
          <motion.button
            key={item.id}
            className={`nav-button ${activeSection === item.id ? 'active' : ''}`}
            data-label={item.label}
            onClick={() => onSectionChange(item.id)}
            onMouseEnter={() => setHoveredItem(item.id)}
            onMouseLeave={() => setHoveredItem(null)}
            initial={{ opacity: 0, x: -50 }}
            animate={{ 
              opacity: 1, 
              x: 0,
              scale: hoveredItem === item.id ? 1.05 : 1
            }}
            transition={{ 
              delay: index * 0.1,
              duration: 0.3
            }}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            {/* Top label */}
            <div className="absolute top-2 left-1/2 transform -translate-x-1/2 text-white font-century-gothic text-xs font-bold tracking-widest uppercase">
              {item.label}
            </div>
            
            {/* Bottom rotated label */}
            <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 -rotate-90 text-white font-century-gothic text-4xl font-normal tracking-wide whitespace-nowrap">
              {item.label}
            </div>
          </motion.button>
        ))}
      </div>
    </nav>
  )
}
