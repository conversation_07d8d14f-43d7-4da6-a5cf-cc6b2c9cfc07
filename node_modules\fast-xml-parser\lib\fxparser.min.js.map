{"version": 3, "file": "./lib/fxparser.min.js", "mappings": "CAAA,SAA2CA,EAAMC,GAC1B,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,IACQ,mBAAXG,QAAyBA,OAAOC,IAC9CD,OAAO,GAAIH,GACe,iBAAZC,QACdA,QAAmB,UAAID,IAEvBD,EAAgB,UAAIC,GACrB,CATD,CASGK,MAAM,I,mBCRT,IAAIC,EAAsB,CCA1BA,EAAwB,CAACL,EAASM,KACjC,IAAI,IAAIC,KAAOD,EACXD,EAAoBG,EAAEF,EAAYC,KAASF,EAAoBG,EAAER,EAASO,IAC5EE,OAAOC,eAAeV,EAASO,EAAK,CAAEI,YAAY,EAAMC,IAAKN,EAAWC,IAE1E,ECNDF,EAAwB,CAACQ,EAAKC,IAAUL,OAAOM,UAAUC,eAAeC,KAAKJ,EAAKC,GCClFT,EAAyBL,IACH,oBAAXkB,QAA0BA,OAAOC,aAC1CV,OAAOC,eAAeV,EAASkB,OAAOC,YAAa,CAAEC,MAAO,WAE7DX,OAAOC,eAAeV,EAAS,aAAc,CAAEoB,OAAO,GAAO,G,oCCJvD,IAAMC,EAAiB,CAC1BC,eAAe,EACfC,oBAAqB,KACrBC,qBAAqB,EACrBC,aAAc,QACdC,kBAAkB,EAClBC,gBAAgB,EAChBC,wBAAwB,EAExBC,eAAe,EACfC,qBAAqB,EACrBC,YAAY,EACZC,eAAe,EACfC,mBAAoB,CAClBC,KAAK,EACLC,cAAc,EACdC,WAAW,GAEbC,kBAAmB,SAASC,EAASC,GACnC,OAAOA,CACT,EACAC,wBAAyB,SAASC,EAAUF,GAC1C,OAAOA,CACT,EACAG,UAAW,GACXC,sBAAsB,EACtBC,QAAS,WAAF,OAAQ,CAAK,EACpBC,iBAAiB,EACjBC,aAAc,GACdC,iBAAiB,EACjBC,cAAc,EACdC,mBAAmB,EACnBC,cAAc,EACdC,kBAAkB,EAClBC,wBAAwB,EACxBC,UAAW,SAASf,EAASgB,EAAOC,GAClC,OAAOjB,CACT,EAEAkB,iBAAiB,GCtCfC,EAAgB,gLAGhBC,EAAY,IAAIC,OAAO,KADGF,EAAgB,KAD/BA,EAEY,mDAEtB,SAASG,EAAcC,EAAQC,GAGpC,IAFA,IAAMC,EAAU,GACZC,EAAQF,EAAMG,KAAKJ,GAChBG,GAAO,CACZ,IAAME,EAAa,GACnBA,EAAWC,WAAaL,EAAMM,UAAYJ,EAAM,GAAGK,OAEnD,IADA,IAAMC,EAAMN,EAAMK,OACTE,EAAQ,EAAGA,EAAQD,EAAKC,IAC/BL,EAAWM,KAAKR,EAAMO,IAExBR,EAAQS,KAAKN,GACbF,EAAQF,EAAMG,KAAKJ,EACrB,CACA,OAAOE,CACT,CAEO,ICrBHU,EDqBSC,EAAS,SAASb,GAE7B,QAAQ,MADMH,EAAUO,KAAKJ,GAE/B,ECrBEY,EADoB,mBAAXvD,OACS,gBAEAA,OAAO,qBAC1B,IAEoByD,EAAO,WAC1B,SAAAA,EAAYC,GACVxE,KAAKwE,QAAUA,EACfxE,KAAKyE,MAAQ,GACbzE,KAAK,MAAQ,CAAC,CAChB,CAAC,IAAA0E,EAAAH,EAAA5D,UAuBA,OAvBA+D,EACDC,IAAA,SAAIxE,EAAIgC,GAAK,IAADyC,EAEC,cAARzE,IAAqBA,EAAM,cAC9BH,KAAKyE,MAAML,OAAIQ,EAAA,IAAIzE,GAAMgC,EAAGyC,GAC9B,EAACF,EACDG,SAAA,SAASC,EAAMf,GAEwC,IAADgB,EAE/CC,EAHe,cAAjBF,EAAKN,UAAyBM,EAAKN,QAAU,cAC7CM,EAAK,OAASzE,OAAO4E,KAAKH,EAAK,OAAOb,OAAS,EAChDjE,KAAKyE,MAAML,OAAIW,EAAA,IAAKD,EAAKN,SAAUM,EAAKL,MAAKM,EAAG,MAAOD,EAAK,MAAKC,IAEjE/E,KAAKyE,MAAML,OAAIY,EAAA,IAAKF,EAAKN,SAAUM,EAAKL,MAAKO,SAG5BE,IAAfnB,IAGF/D,KAAKyE,MAAMzE,KAAKyE,MAAMR,OAAS,GAAGI,GAAmB,CAAEN,WAAAA,GAE3D,EACAQ,EACOY,kBAAP,WACE,OAAOd,CACT,EAACE,CAAA,CA5ByB,GCPb,SAASa,EAAYC,EAASC,GAEzC,IAAMC,EAAW,CAAC,EAClB,GAAuB,MAAnBF,EAAQC,EAAI,IACQ,MAAnBD,EAAQC,EAAI,IACO,MAAnBD,EAAQC,EAAI,IACO,MAAnBD,EAAQC,EAAI,IACO,MAAnBD,EAAQC,EAAI,IACO,MAAnBD,EAAQC,EAAI,GAyDb,MAAM,IAAIE,MAAM,kCAvDhBF,GAAM,EAIN,IAHA,IAAIG,EAAqB,EACrBC,GAAU,EAAOC,GAAU,EAE1BL,EAAED,EAAQpB,OAAOqB,IAClB,GAAmB,MAAfD,EAAQC,IAAeK,EA4BpB,GAAmB,MAAfN,EAAQC,IASf,GARGK,EACwB,MAAnBN,EAAQC,EAAI,IAAiC,MAAnBD,EAAQC,EAAI,KACtCK,GAAU,EACVF,KAGJA,IAEuB,IAAvBA,EACF,UAEmB,MAAfJ,EAAQC,GACdI,GAAU,EAEHL,EAAQC,OA3CiB,CAChC,GAAII,GAAWE,EAAOP,EAAS,UAAUC,GAAG,CAExC,IAAIO,EAAY1D,OAAG,EAAC2D,EACEC,EAAcV,GAFpCC,GAAK,GAEyC,GAA7CO,EAAUC,EAAA,GAAE3D,EAAG2D,EAAA,GAACR,EAACQ,EAAA,IACO,IAAtB3D,EAAI6D,QAAQ,OACXT,EAAUM,GAAe,CACrBI,KAAO1C,OAAO,IAAKsC,EAAU,IAAI,KACjC1D,IAAKA,GAEjB,MACK,GAAIuD,GAAWE,EAAOP,EAAS,WAAWC,GAG3CA,EADgBY,EAAeb,GAD/BC,GAAK,GACoC,GAAlCnB,WAEL,GAAIuB,GAAWE,EAAOP,EAAS,WAAWC,GAC5CA,GAAK,OAGH,GAAII,GAAWE,EAAOP,EAAS,YAAYC,GAG7CA,EADgBa,EAAgBd,GADhCC,GAAK,GACqC,GAAnCnB,UAEL,KAAIyB,EAAOP,EAAS,MAAMC,GAC3B,MAAM,IAAIE,MAAM,mBADgBG,GAAU,CACR,CAEvCF,GAEJ,CAkBJ,GAA0B,IAAvBA,EACC,MAAM,IAAID,MAAM,oBAKxB,MAAO,CAACD,SAAAA,EAAUD,EAAAA,EACtB,CAEA,IAAMc,EAAiB,SAACC,EAAMlC,GAC1B,KAAOA,EAAQkC,EAAKpC,QAAU,KAAKqC,KAAKD,EAAKlC,KACzCA,IAEJ,OAAOA,CACX,EAEA,SAAS4B,EAAcV,EAASC,GAW5BA,EAAIc,EAAef,EAASC,GAI5B,IADA,IAAIO,EAAa,GACVP,EAAID,EAAQpB,SAAW,KAAKqC,KAAKjB,EAAQC,KAAsB,MAAfD,EAAQC,IAA6B,MAAfD,EAAQC,IACjFO,GAAcR,EAAQC,GACtBA,IAQJ,GANAiB,EAAmBV,GAGnBP,EAAIc,EAAef,EAASC,GAGsB,WAA9CD,EAAQmB,UAAUlB,EAAGA,EAAI,GAAGmB,cAC5B,MAAM,IAAIjB,MAAM,uCACd,GAAmB,MAAfH,EAAQC,GACd,MAAM,IAAIE,MAAM,wCAIpB,IAAqBkB,EACFC,EAAkBtB,EAASC,EAAG,UAEjD,OAFCA,EAACoB,EAAA,GAEK,CAACb,EAFOa,EAAA,KACfpB,EAEJ,CAEA,SAASa,EAAgBd,EAASC,GAE9BA,EAAIc,EAAef,EAASC,GAI5B,IADA,IAAIsB,EAAe,GACZtB,EAAID,EAAQpB,SAAW,KAAKqC,KAAKjB,EAAQC,KAC5CsB,GAAgBvB,EAAQC,GACxBA,IAEJiB,EAAmBK,GAGnBtB,EAAIc,EAAef,EAASC,GAG5B,IAAMuB,EAAiBxB,EAAQmB,UAAUlB,EAAGA,EAAI,GAAGmB,cACnD,GAAuB,WAAnBI,GAAkD,WAAnBA,EAC/B,MAAM,IAAIrB,MAAM,qCAAqCqB,EAAc,KAEvEvB,GAAKuB,EAAe5C,OAGpBqB,EAAIc,EAAef,EAASC,GAG5B,IAAIwB,EAAmB,KACnBC,EAAmB,KAEvB,GAAuB,WAAnBF,EAA6B,CAG7B,IAAAG,EAFyBL,EAAkBtB,EAASC,EAAG,oBAMvD,GANCA,EAAC0B,EAAA,GAAEF,EAAgBE,EAAA,GAMD,MAAf3B,EAHJC,EAAIc,EAAef,EAASC,KAGa,MAAfD,EAAQC,GAAY,CAAC,IAAD2B,EACjBN,EAAkBtB,EAASC,EAAE,oBAArDA,EAAC2B,EAAA,GAAEF,EAAgBE,EAAA,EACxB,CACJ,MAAO,GAAuB,WAAnBJ,EAA6B,CACpC,IAAAK,EACyBP,EAAkBtB,EAASC,EAAG,oBAEvD,GAFCA,EAAC4B,EAAA,KAAEH,EAAgBG,EAAA,IAGhB,MAAM,IAAI1B,MAAM,0DAExB,CAEA,MAAO,CAACoB,aAAAA,EAAcE,iBAAAA,EAAkBC,iBAAAA,EAAkB5C,QAASmB,EACvE,CAEA,SAASqB,EAAkBtB,EAASC,EAAG6B,GACnC,IAAIC,EAAgB,GACdC,EAAYhC,EAAQC,GAC1B,GAAkB,MAAd+B,GAAmC,MAAdA,EACrB,MAAM,IAAI7B,MAAM,kCAAkC6B,EAAS,KAI/D,IAFA/B,IAEOA,EAAID,EAAQpB,QAAUoB,EAAQC,KAAO+B,GACxCD,GAAiB/B,EAAQC,GACzBA,IAGJ,GAAID,EAAQC,KAAO+B,EACf,MAAM,IAAI7B,MAAM,gBAAgB2B,EAAI,UAGxC,MAAO,GADP7B,EACW8B,EACf,CAEA,SAASlB,EAAeb,EAASC,GAQ7BA,EAAIc,EAAef,EAASC,GAI5B,IADA,IAAIgC,EAAc,GACXhC,EAAID,EAAQpB,SAAW,KAAKqC,KAAKjB,EAAQC,KAC5CgC,GAAejC,EAAQC,GACvBA,IAIJ,IAAKiB,EAAmBe,GACpB,MAAM,IAAI9B,MAAM,0BAA0B8B,EAAW,KAKzD,IAAIC,EAAe,GAEnB,GAAkB,MAAflC,EAHHC,EAAIc,EAAef,EAASC,KAGHM,EAAOP,EAAS,OAAOC,GAAIA,GAAG,OAClD,GAAkB,MAAfD,EAAQC,IAAcM,EAAOP,EAAS,KAAKC,GAAIA,GAAG,MACrD,IAAmB,MAAfD,EAAQC,GAab,MAAM,IAAIE,MAAM,sCAAsCH,EAAQC,GAAE,KAThE,IAHAA,IAGOA,EAAID,EAAQpB,QAAyB,MAAfoB,EAAQC,IACjCiC,GAAgBlC,EAAQC,GACxBA,IAEJ,GAAmB,MAAfD,EAAQC,GACR,MAAM,IAAIE,MAAM,6BAKxB,CAEA,MAAO,CACH8B,YAAAA,EACAC,aAAcA,EAAaC,OAC3BrD,MAAOmB,EAEf,CAsHA,SAASM,EAAOS,EAAMoB,EAAInC,GACtB,IAAI,IAAIoC,EAAE,EAAEA,EAAED,EAAIxD,OAAOyD,IACrB,GAAGD,EAAIC,KAAKrB,EAAKf,EAAEoC,EAAE,GAAI,OAAO,EAEpC,OAAO,CACX,CAEA,SAASnB,EAAmBoB,GACxB,GAAIrD,EAAOqD,GACd,OAAOA,EAEA,MAAM,IAAInC,MAAM,uBAAuBmC,EAC/C,CChXA,MAAMC,EAAW,wBACXC,EAAW,qCAKXC,EAAW,CACbhG,KAAO,EAEPC,cAAc,EACdgG,aAAc,IACd/F,WAAW,GAqEf,MAAMgG,EAAgB,0C,sGClEtB,IAEqBC,EACnB,SAAYC,GCjBC,IAA+B5G,EDkB1CtB,KAAKkI,QAAUA,EACflI,KAAKmI,YAAc,KACnBnI,KAAKoI,cAAgB,GACrBpI,KAAKqI,gBAAkB,CAAC,EACxBrI,KAAKsI,aAAe,CAClB,KAAS,CAAE5E,MAAO,qBAAsBvB,IAAM,KAC9C,GAAO,CAAEuB,MAAO,mBAAoBvB,IAAM,KAC1C,GAAO,CAAEuB,MAAO,mBAAoBvB,IAAM,KAC1C,KAAS,CAAEuB,MAAO,qBAAsBvB,IAAM,MAEhDnC,KAAKuI,UAAY,CAAE7E,MAAO,oBAAqBvB,IAAM,KACrDnC,KAAK4C,aAAe,CAClB,MAAS,CAAEc,MAAO,iBAAkBvB,IAAK,KAMzC,KAAS,CAAEuB,MAAO,iBAAkBvB,IAAK,KACzC,MAAU,CAAEuB,MAAO,kBAAmBvB,IAAK,KAC3C,IAAQ,CAAEuB,MAAO,gBAAiBvB,IAAK,KACvC,KAAS,CAAEuB,MAAO,kBAAmBvB,IAAK,KAC1C,UAAc,CAAEuB,MAAO,iBAAkBvB,IAAK,KAC9C,IAAQ,CAAEuB,MAAO,gBAAiBvB,IAAK,KACvC,IAAQ,CAAEuB,MAAO,iBAAkBvB,IAAK,KACxC,QAAW,CAAEuB,MAAO,mBAAoBvB,IAAM,SAACqG,EAAGC,GAAG,OAAKC,OAAOC,cAAcC,OAAOC,SAASJ,EAAK,IAAI,GACxG,QAAW,CAAE/E,MAAO,0BAA2BvB,IAAM,SAACqG,EAAGC,GAAG,OAAKC,OAAOC,cAAcC,OAAOC,SAASJ,EAAK,IAAI,IAEjHzI,KAAK8I,oBAAsBA,EAC3B9I,KAAK+I,SAAWA,EAChB/I,KAAKgJ,cAAgBA,EACrBhJ,KAAKiJ,iBAAmBA,EACxBjJ,KAAKkJ,mBAAqBA,EAC1BlJ,KAAKmJ,aAAeA,EACpBnJ,KAAKoJ,qBAAuBA,EAC5BpJ,KAAKqJ,iBAAmBA,EACxBrJ,KAAKsJ,oBAAsBA,EAC3BtJ,KAAK6E,SAAWA,EAChB7E,KAAKuJ,mBCvD2B,mBADUjI,EDwDMtB,KAAKkI,QAAQ5G,kBCtDlDA,EAEPkI,MAAMhH,QAAQlB,GACP,SAACe,GACJ,QAAsCoH,EAAtCC,E,4rBAAAC,CAAsBrI,KAAgBmI,EAAAC,KAAAE,MAAE,CAAC,IAA9BC,EAAOJ,EAAAzI,MACd,GAAuB,iBAAZ6I,GAAwBxH,IAAawH,EAC5C,OAAO,EAEX,GAAIA,aAAmBtG,QAAUsG,EAAQvD,KAAKjE,GAC1C,OAAO,CAEf,CACJ,EAEG,kBAAM,CAAK,CDyCpB,EAIF,SAASyG,EAAoBgB,GAE3B,IADA,IAAMC,EAAU1J,OAAO4E,KAAK6E,GACnBxE,EAAI,EAAGA,EAAIyE,EAAQ9F,OAAQqB,IAAK,CACvC,IAAM0E,EAAMD,EAAQzE,GACpBtF,KAAKsI,aAAa0B,GAAO,CACtBtG,MAAO,IAAIH,OAAO,IAAIyG,EAAI,IAAI,KAC9B7H,IAAM2H,EAAiBE,GAE5B,CACF,CAWA,SAAShB,EAAc7G,EAAKD,EAASgB,EAAO+G,EAAUC,EAAeC,EAAYC,GAC/E,QAAYlF,IAAR/C,IACEnC,KAAKkI,QAAQvG,aAAesI,IAC9B9H,EAAMA,EAAIqF,QAETrF,EAAI8B,OAAS,GAAE,CACZmG,IAAgBjI,EAAMnC,KAAKoJ,qBAAqBjH,IAEpD,IAAMkI,EAASrK,KAAKkI,QAAQjG,kBAAkBC,EAASC,EAAKe,EAAOgH,EAAeC,GAClF,OAAGE,QAEMlI,SACOkI,UAAkBlI,GAAOkI,IAAWlI,EAE3CkI,EACArK,KAAKkI,QAAQvG,YAGDQ,EAAIqF,SACLrF,EAHXmI,EAAWnI,EAAKnC,KAAKkI,QAAQzG,cAAezB,KAAKkI,QAAQrG,oBAMvDM,CAGb,CAEJ,CAEA,SAAS8G,EAAiBzE,GACxB,GAAIxE,KAAKkI,QAAQ3G,eAAgB,CAC/B,IAAMgJ,EAAO/F,EAAQgG,MAAM,KACrBC,EAA+B,MAAtBjG,EAAQkG,OAAO,GAAa,IAAM,GACjD,GAAgB,UAAZH,EAAK,GACP,MAAO,GAEW,IAAhBA,EAAKtG,SACPO,EAAUiG,EAASF,EAAK,GAE5B,CACA,OAAO/F,CACT,CAIA,IAAMmG,EAAY,IAAIpH,OAAO,+CAAgD,MAE7E,SAAS2F,EAAmB0B,EAAS1H,EAAOhB,GAC1C,IAAsC,IAAlClC,KAAKkI,QAAQ5G,kBAAgD,iBAAZsJ,EAAsB,CAOzE,IAHA,IAAMjH,EAAUH,EAAcoH,EAASD,GACjCzG,EAAMP,EAAQM,OACdd,EAAQ,CAAC,EACNmC,EAAI,EAAGA,EAAIpB,EAAKoB,IAAK,CAC5B,IAAMjD,EAAWrC,KAAKiJ,iBAAiBtF,EAAQ2B,GAAG,IAClD,IAAItF,KAAKuJ,mBAAmBlH,EAAUa,GAAtC,CAGA,IAAI2H,EAASlH,EAAQ2B,GAAG,GACpBwF,EAAQ9K,KAAKkI,QAAQ/G,oBAAsBkB,EAC/C,GAAIA,EAAS4B,OAKX,GAJIjE,KAAKkI,QAAQlF,yBACf8H,EAAQ9K,KAAKkI,QAAQlF,uBAAuB8H,IAEjC,cAAVA,IAAuBA,EAAS,mBACpB5F,IAAX2F,EAAsB,CACpB7K,KAAKkI,QAAQvG,aACfkJ,EAASA,EAAOrD,QAElBqD,EAAS7K,KAAKoJ,qBAAqByB,GACnC,IAAME,EAAS/K,KAAKkI,QAAQ9F,wBAAwBC,EAAUwI,EAAQ3H,GAGpEC,EAAM2H,GAFLC,QAEcF,SACDE,UAAkBF,GAAUE,IAAWF,EAEtCE,EAGAT,EACbO,EACA7K,KAAKkI,QAAQxG,oBACb1B,KAAKkI,QAAQrG,mBAGnB,MAAW7B,KAAKkI,QAAQ1G,yBACtB2B,EAAM2H,IAAS,EA7BnB,CAgCF,CACA,IAAKzK,OAAO4E,KAAK9B,GAAOc,OACtB,OAEF,GAAIjE,KAAKkI,QAAQ9G,oBAAqB,CACpC,IAAM4J,EAAiB,CAAC,EAExB,OADAA,EAAehL,KAAKkI,QAAQ9G,qBAAuB+B,EAC5C6H,CACT,CACA,OAAO7H,CACT,CACF,CAEA,IAAM4F,EAAW,SAAS1D,GACxBA,EAAUA,EAAQ4F,QAAQ,SAAU,MAKpC,IAJA,IAAMC,EAAS,IAAIC,EAAQ,QACvBhD,EAAc+C,EACdE,EAAW,GACXlI,EAAQ,GACJoC,EAAE,EAAGA,EAAGD,EAAQpB,OAAQqB,IAE9B,GAAU,MADCD,EAAQC,GAIjB,GAAqB,MAAjBD,EAAQC,EAAE,GAAY,CACxB,IAAM+F,EAAaC,EAAiBjG,EAAS,IAAKC,EAAG,8BACjDpD,EAAUmD,EAAQmB,UAAUlB,EAAE,EAAE+F,GAAY7D,OAEhD,GAAGxH,KAAKkI,QAAQ3G,eAAe,CAC7B,IAAMgK,EAAarJ,EAAQ8D,QAAQ,MAChB,IAAhBuF,IACDrJ,EAAUA,EAAQsJ,OAAOD,EAAW,GAExC,CAEGvL,KAAKkI,QAAQnF,mBACdb,EAAUlC,KAAKkI,QAAQnF,iBAAiBb,IAGvCiG,IACDiD,EAAWpL,KAAKsJ,oBAAoB8B,EAAUjD,EAAajF,IAI7D,IAAMuI,EAAcvI,EAAMsD,UAAUtD,EAAMwI,YAAY,KAAK,GAC3D,GAAGxJ,IAA2D,IAAhDlC,KAAKkI,QAAQxF,aAAasD,QAAQ9D,GAC9C,MAAM,IAAIsD,MAAM,kDAAkDtD,EAAO,KAE3E,IAAIyJ,EAAY,EACbF,IAAmE,IAApDzL,KAAKkI,QAAQxF,aAAasD,QAAQyF,IAClDE,EAAYzI,EAAMwI,YAAY,IAAKxI,EAAMwI,YAAY,KAAK,GAC1D1L,KAAKoI,cAAcwD,OAEnBD,EAAYzI,EAAMwI,YAAY,KAEhCxI,EAAQA,EAAMsD,UAAU,EAAGmF,GAE3BxD,EAAcnI,KAAKoI,cAAcwD,MACjCR,EAAW,GACX9F,EAAI+F,CACN,MAAO,GAAqB,MAAjBhG,EAAQC,EAAE,GAAY,CAE/B,IAAIuG,EAAUC,EAAWzG,EAAQC,GAAG,EAAO,MAC3C,IAAIuG,EAAS,MAAM,IAAIrG,MAAM,yBAG7B,GADA4F,EAAWpL,KAAKsJ,oBAAoB8B,EAAUjD,EAAajF,GACtDlD,KAAKkI,QAAQrF,mBAAyC,SAApBgJ,EAAQ3J,SAAuBlC,KAAKkI,QAAQpF,kBAE9E,CAEH,IAAMiJ,EAAY,IAAIZ,EAAQU,EAAQ3J,SACtC6J,EAAUpH,IAAI3E,KAAKkI,QAAQ7G,aAAc,IAEtCwK,EAAQ3J,UAAY2J,EAAQG,QAAUH,EAAQI,iBAC/CF,EAAU,MAAQ/L,KAAKkJ,mBAAmB2C,EAAQG,OAAQ9I,EAAO2I,EAAQ3J,UAE3ElC,KAAK6E,SAASsD,EAAa4D,EAAW7I,EAAOoC,EAC/C,CAGAA,EAAIuG,EAAQR,WAAa,CAC3B,MAAO,GAAgC,QAA7BhG,EAAQmG,OAAOlG,EAAI,EAAG,GAAc,CAC5C,IAAM4G,EAAWZ,EAAiBjG,EAAS,SAAOC,EAAE,EAAG,0BACvD,GAAGtF,KAAKkI,QAAQzF,gBAAgB,CAAC,IAAD0J,EACxBxG,EAAUN,EAAQmB,UAAUlB,EAAI,EAAG4G,EAAW,GAEpDd,EAAWpL,KAAKsJ,oBAAoB8B,EAAUjD,EAAajF,GAE3DiF,EAAYxD,IAAI3E,KAAKkI,QAAQzF,gBAAiB,EAAA0J,EAAA,GAAAA,EAAKnM,KAAKkI,QAAQ7G,cAAgBsE,EAAOwG,IACzF,CACA7G,EAAI4G,CACN,MAAO,GAAiC,OAA7B7G,EAAQmG,OAAOlG,EAAI,EAAG,GAAa,CAC5C,IAAM8G,EAAShH,EAAYC,EAASC,GACpCtF,KAAKqI,gBAAkB+D,EAAO7G,SAC9BD,EAAI8G,EAAO9G,CACb,MAAM,GAAgC,OAA7BD,EAAQmG,OAAOlG,EAAI,EAAG,GAAa,CAC1C,IAAM+F,EAAaC,EAAiBjG,EAAS,MAAOC,EAAG,wBAA0B,EAC3E0G,EAAS3G,EAAQmB,UAAUlB,EAAI,EAAE+F,GAEvCD,EAAWpL,KAAKsJ,oBAAoB8B,EAAUjD,EAAajF,GAE3D,IAI8BmJ,EAJ1BlK,EAAMnC,KAAKgJ,cAAcgD,EAAQ7D,EAAY3D,QAAStB,GAAO,GAAM,GAAO,GAAM,GAC1EgC,MAAP/C,IAAkBA,EAAM,IAGxBnC,KAAKkI,QAAQtG,cACduG,EAAYxD,IAAI3E,KAAKkI,QAAQtG,cAAe,EAAAyK,EAAA,GAAAA,EAAKrM,KAAKkI,QAAQ7G,cAAgB2K,EAAMK,KAEpFlE,EAAYxD,IAAI3E,KAAKkI,QAAQ7G,aAAcc,GAG7CmD,EAAI+F,EAAa,CACnB,KAAM,CACJ,IAAIe,EAASN,EAAWzG,EAAQC,EAAGtF,KAAKkI,QAAQ3G,gBAC5CW,EAASkK,EAAOlK,QACdoK,EAAaF,EAAOE,WACtBN,EAASI,EAAOJ,OAChBC,EAAiBG,EAAOH,eACxBZ,EAAae,EAAOf,WAEpBrL,KAAKkI,QAAQnF,mBACfb,EAAUlC,KAAKkI,QAAQnF,iBAAiBb,IAItCiG,GAAeiD,GACU,SAAxBjD,EAAY3D,UAEb4G,EAAWpL,KAAKsJ,oBAAoB8B,EAAUjD,EAAajF,GAAO,IAKtE,IAAMqJ,EAAUpE,EACboE,IAAmE,IAAxDvM,KAAKkI,QAAQxF,aAAasD,QAAQuG,EAAQ/H,WACtD2D,EAAcnI,KAAKoI,cAAcwD,MACjC1I,EAAQA,EAAMsD,UAAU,EAAGtD,EAAMwI,YAAY,OAE5CxJ,IAAYgJ,EAAO1G,UACpBtB,GAASA,EAAQ,IAAMhB,EAAUA,GAEnC,IAAM6B,EAAauB,EACnB,GAAItF,KAAKmJ,aAAanJ,KAAKkI,QAAQ5F,UAAWY,EAAOhB,GAAU,CAC7D,IAAIsK,EAAa,GAEjB,GAAGR,EAAO/H,OAAS,GAAK+H,EAAON,YAAY,OAASM,EAAO/H,OAAS,EAC/B,MAAhC/B,EAAQA,EAAQ+B,OAAS,IAC1B/B,EAAUA,EAAQsJ,OAAO,EAAGtJ,EAAQ+B,OAAS,GAC7Cf,EAAQA,EAAMsI,OAAO,EAAGtI,EAAMe,OAAS,GACvC+H,EAAS9J,GAET8J,EAASA,EAAOR,OAAO,EAAGQ,EAAO/H,OAAS,GAE5CqB,EAAI8G,EAAOf,gBAGR,IAAmD,IAAhDrL,KAAKkI,QAAQxF,aAAasD,QAAQ9D,GAExCoD,EAAI8G,EAAOf,eAGT,CAEF,IAAMe,EAASpM,KAAKqJ,iBAAiBhE,EAASiH,EAAYjB,EAAa,GACvE,IAAIe,EAAQ,MAAM,IAAI5G,MAAM,qBAAqB8G,GACjDhH,EAAI8G,EAAO9G,EACXkH,EAAaJ,EAAOI,UACtB,CAEA,IAAMT,EAAY,IAAIZ,EAAQjJ,GAE3BA,IAAY8J,GAAUC,IACvBF,EAAU,MAAQ/L,KAAKkJ,mBAAmB8C,EAAQ9I,EAAOhB,IAExDsK,IACDA,EAAaxM,KAAKgJ,cAAcwD,EAAYtK,EAASgB,GAAO,EAAM+I,GAAgB,GAAM,IAG1F/I,EAAQA,EAAMsI,OAAO,EAAGtI,EAAMwI,YAAY,MAC1CK,EAAUpH,IAAI3E,KAAKkI,QAAQ7G,aAAcmL,GAEzCxM,KAAK6E,SAASsD,EAAa4D,EAAW7I,EAAOa,EAC/C,KAAK,CAEH,GAAGiI,EAAO/H,OAAS,GAAK+H,EAAON,YAAY,OAASM,EAAO/H,OAAS,EAAE,CACjC,MAAhC/B,EAAQA,EAAQ+B,OAAS,IAC1B/B,EAAUA,EAAQsJ,OAAO,EAAGtJ,EAAQ+B,OAAS,GAC7Cf,EAAQA,EAAMsI,OAAO,EAAGtI,EAAMe,OAAS,GACvC+H,EAAS9J,GAET8J,EAASA,EAAOR,OAAO,EAAGQ,EAAO/H,OAAS,GAGzCjE,KAAKkI,QAAQnF,mBACdb,EAAUlC,KAAKkI,QAAQnF,iBAAiBb,IAG1C,IAAM6J,EAAY,IAAIZ,EAAQjJ,GAC3BA,IAAY8J,GAAUC,IACvBF,EAAU,MAAQ/L,KAAKkJ,mBAAmB8C,EAAQ9I,EAAOhB,IAE3DlC,KAAK6E,SAASsD,EAAa4D,EAAW7I,EAAOa,GAC7Cb,EAAQA,EAAMsI,OAAO,EAAGtI,EAAMwI,YAAY,KAC5C,KAEI,CACF,IAAMK,EAAY,IAAIZ,EAASjJ,GAC/BlC,KAAKoI,cAAchE,KAAK+D,GAErBjG,IAAY8J,GAAUC,IACvBF,EAAU,MAAQ/L,KAAKkJ,mBAAmB8C,EAAQ9I,EAAOhB,IAE3DlC,KAAK6E,SAASsD,EAAa4D,EAAW7I,EAAOa,GAC7CoE,EAAc4D,CAChB,CACAX,EAAW,GACX9F,EAAI+F,CACN,CACF,MAEAD,GAAY/F,EAAQC,GAGxB,OAAO4F,EAAOzG,KAChB,EAEA,SAASI,EAASsD,EAAa4D,EAAW7I,EAAOa,GAE1C/D,KAAKkI,QAAQ9E,kBAAiBW,OAAamB,GAChD,IAAMkH,EAASpM,KAAKkI,QAAQjF,UAAU8I,EAAUvH,QAAStB,EAAO6I,EAAU,QAC5D,IAAXK,IACyB,iBAAXA,GACfL,EAAUvH,QAAU4H,EACpBjE,EAAYtD,SAASkH,EAAWhI,IAEhCoE,EAAYtD,SAASkH,EAAWhI,GAEpC,CAEA,IAAMqF,EAAuB,SAASjH,GAEpC,GAAGnC,KAAKkI,QAAQvF,gBAAgB,CAC9B,IAAI,IAAIkD,KAAc7F,KAAKqI,gBAAgB,CACzC,IAAMoE,EAASzM,KAAKqI,gBAAgBxC,GACpC1D,EAAMA,EAAI8I,QAASwB,EAAOxG,KAAMwG,EAAOtK,IACzC,CACA,IAAI,IAAI0D,KAAc7F,KAAKsI,aAAa,CACtC,IAAMmE,EAASzM,KAAKsI,aAAazC,GACjC1D,EAAMA,EAAI8I,QAASwB,EAAO/I,MAAO+I,EAAOtK,IAC1C,CACA,GAAGnC,KAAKkI,QAAQtF,aACd,IAAI,IAAIiD,KAAc7F,KAAK4C,aAAa,CACtC,IAAM6J,EAASzM,KAAK4C,aAAaiD,GACjC1D,EAAMA,EAAI8I,QAASwB,EAAO/I,MAAO+I,EAAOtK,IAC1C,CAEFA,EAAMA,EAAI8I,QAASjL,KAAKuI,UAAU7E,MAAO1D,KAAKuI,UAAUpG,IAC1D,CACA,OAAOA,CACT,EACA,SAASmH,EAAoB8B,EAAUjD,EAAajF,EAAOiH,GAezD,OAdIiB,SACgBlG,IAAfiF,IAA0BA,EAA0C,IAA7BhC,EAAY1D,MAAMR,aAS3CiB,KAPjBkG,EAAWpL,KAAKgJ,cAAcoC,EAC5BjD,EAAY3D,QACZtB,GACA,IACAiF,EAAY,OAAkD,IAA1C9H,OAAO4E,KAAKkD,EAAY,OAAOlE,OACnDkG,KAEyC,KAAbiB,GAC5BjD,EAAYxD,IAAI3E,KAAKkI,QAAQ7G,aAAc+J,GAC7CA,EAAW,IAENA,CACT,CASA,SAASjC,EAAa7G,EAAWY,EAAOwJ,GACtC,IAAMC,EAAc,KAAOD,EAC3B,IAAK,IAAME,KAAgBtK,EAAW,CACpC,IAAMuK,EAAcvK,EAAUsK,GAC9B,GAAID,IAAgBE,GAAe3J,IAAU2J,EAAe,OAAO,CACrE,CACA,OAAO,CACT,CAsCA,SAASvB,EAAiBjG,EAASoD,EAAKnD,EAAGwH,GACzC,IAAMC,EAAe1H,EAAQW,QAAQyC,EAAKnD,GAC1C,IAAqB,IAAlByH,EACD,MAAM,IAAIvH,MAAMsH,GAEhB,OAAOC,EAAetE,EAAIxE,OAAS,CAEvC,CAEA,SAAS6H,EAAWzG,EAAQC,EAAG/D,EAAgByL,QAAW,IAAXA,IAAAA,EAAc,KAC3D,IAAMZ,EAxCR,SAAgC/G,EAASC,EAAG0H,GAC1C,IAAIC,OADiD,IAAXD,IAAAA,EAAc,KAGxD,IADA,IAAIhB,EAAS,GACJ7H,EAAQmB,EAAGnB,EAAQkB,EAAQpB,OAAQE,IAAS,CACnD,IAAI+I,EAAK7H,EAAQlB,GACjB,GAAI8I,EACIC,IAAOD,IAAcA,EAAe,SACrC,GAAW,MAAPC,GAAqB,MAAPA,EACrBD,EAAeC,OACZ,GAAIA,IAAOF,EAAY,GAAI,CAChC,IAAGA,EAAY,GAQb,MAAO,CACL3G,KAAM2F,EACN7H,MAAOA,GATT,GAAGkB,EAAQlB,EAAQ,KAAO6I,EAAY,GACpC,MAAO,CACL3G,KAAM2F,EACN7H,MAAOA,EASf,KAAkB,OAAP+I,IACTA,EAAK,KAEPlB,GAAUkB,CACZ,CACF,CAYiBC,CAAuB9H,EAASC,EAAE,EAAG0H,GACpD,GAAIZ,EAAJ,CACA,IAAIJ,EAASI,EAAO/F,KACdgF,EAAae,EAAOjI,MACpBiJ,EAAiBpB,EAAOqB,OAAO,MACjCnL,EAAU8J,EACVC,GAAiB,GACE,IAApBmB,IACDlL,EAAU8J,EAAOxF,UAAU,EAAG4G,GAC9BpB,EAASA,EAAOxF,UAAU4G,EAAiB,GAAGE,aAGhD,IAAMhB,EAAapK,EACnB,GAAGX,EAAe,CAChB,IAAMgK,EAAarJ,EAAQ8D,QAAQ,MAChB,IAAhBuF,IAEDU,GADA/J,EAAUA,EAAQsJ,OAAOD,EAAW,MACPa,EAAO/F,KAAKmF,OAAOD,EAAa,GAEjE,CAEA,MAAO,CACLrJ,QAASA,EACT8J,OAAQA,EACRX,WAAYA,EACZY,eAAgBA,EAChBK,WAAYA,EAzBI,CA2BpB,CAOA,SAASjD,EAAiBhE,EAASnD,EAASoD,GAK1C,IAJA,IAAMvB,EAAauB,EAEfiI,EAAe,EAEZjI,EAAID,EAAQpB,OAAQqB,IACzB,GAAmB,MAAfD,EAAQC,GACV,GAAqB,MAAjBD,EAAQC,EAAE,GAAY,CACtB,IAAM+F,EAAaC,EAAiBjG,EAAS,IAAKC,EAAMpD,EAAO,kBAE/D,GADmBmD,EAAQmB,UAAUlB,EAAE,EAAE+F,GAAY7D,SACjCtF,GAEG,KADrBqL,EAEE,MAAO,CACLf,WAAYnH,EAAQmB,UAAUzC,EAAYuB,GAC1CA,EAAI+F,GAIV/F,EAAE+F,CACJ,MAAO,GAAoB,MAAjBhG,EAAQC,EAAE,GAElBA,EADmBgG,EAAiBjG,EAAS,KAAMC,EAAE,EAAG,gCAEnD,GAAgC,QAA7BD,EAAQmG,OAAOlG,EAAI,EAAG,GAE9BA,EADmBgG,EAAiBjG,EAAS,SAAOC,EAAE,EAAG,gCAEpD,GAAgC,OAA7BD,EAAQmG,OAAOlG,EAAI,EAAG,GAE9BA,EADmBgG,EAAiBjG,EAAS,MAAOC,EAAG,2BAA6B,MAE/E,CACL,IAAMuG,EAAUC,EAAWzG,EAASC,EAAG,KAEnCuG,KACkBA,GAAWA,EAAQ3J,WACnBA,GAAuD,MAA5C2J,EAAQG,OAAOH,EAAQG,OAAO/H,OAAO,IAClEsJ,IAEFjI,EAAEuG,EAAQR,WAEd,CAGR,CAEA,SAASf,EAAWnI,EAAKqL,EAAatF,GACpC,GAAIsF,GAA8B,iBAARrL,EAAkB,CAE1C,IAAMkI,EAASlI,EAAIqF,OACnB,MAAc,SAAX6C,GACgB,UAAXA,GDrkBG,SAAkB5B,EAAKP,EAAU,CAAC,GAE7C,GADAA,EAAU7H,OAAOoN,OAAO,CAAC,EAAG3F,EAAUI,IAClCO,GAAsB,iBAARA,EAAmB,OAAOA,EAE5C,IAAIiF,EAAcjF,EAAIjB,OAEtB,QAAwBtC,IAArBgD,EAAQyF,UAA0BzF,EAAQyF,SAASrH,KAAKoH,GAAa,OAAOjF,EAC1E,GAAS,MAANA,EAAW,OAAO,EACrB,GAAIP,EAAQpG,KAAO8F,EAAStB,KAAKoH,GAClC,OAkGR,SAAmBE,GAEf,GAAG/E,SAAU,OAAOA,SAAS+E,EApGI,IAqG5B,GAAGhF,OAAOC,SAAU,OAAOD,OAAOC,SAAS+E,EArGf,IAsG5B,GAAGC,QAAUA,OAAOhF,SAAU,OAAOgF,OAAOhF,SAAS+E,EAtGzB,IAuG5B,MAAM,IAAIpI,MAAM,+DACzB,CAxGesI,CAAUJ,GAGf,IAAsC,IAAlCA,EAAWL,OAAO,YACxB,OAqDR,SAA0B5E,EAAIiF,EAAWxF,GACrC,IAAIA,EAAQlG,UAAW,OAAOyG,EAC9B,MAAMsF,EAAWL,EAAW9J,MAAMoE,GAClC,GAAG+F,EAAS,CACR,IAAIC,EAAOD,EAAS,IAAM,GAC1B,MAAME,GAAsC,IAA9BF,EAAS,GAAG/H,QAAQ,KAAc,IAAM,IAChDjE,EAAegM,EAAS,GACxBG,EAA0BF,EAC5BvF,EAAI1G,EAAakC,OAAO,KAAOgK,EAC7BxF,EAAI1G,EAAakC,UAAYgK,EAEnC,OAAGlM,EAAakC,OAAS,GAAKiK,EAAgCzF,EAC9B,IAAxB1G,EAAakC,SACb8J,EAAS,GAAGI,WAAW,IAAIF,MAAYF,EAAS,GAAG,KAAOE,EAEzD/F,EAAQnG,eAAiBmM,GAE9BR,GAAcK,EAAS,IAAM,IAAMA,EAAS,GACrCnF,OAAO8E,IACLjF,EALEG,OAAO8E,EAM1B,CACI,OAAOjF,CAEf,CA5Ee2F,CAAiB3F,EAAIiF,EAAWxF,GAGtC,CAED,MAAMtE,EAAQiE,EAAShE,KAAK6J,GAE5B,GAAG9J,EAAM,CACL,MAAMoK,EAAOpK,EAAM,IAAM,GACnB7B,EAAe6B,EAAM,GAC3B,IAAIyK,GAyEGT,EAzE2BhK,EAAM,MA0EV,IAAzBgK,EAAO5H,QAAQ,MAEV,OADd4H,EAASA,EAAO3C,QAAQ,MAAO,KACX2C,EAAS,IACP,MAAdA,EAAO,GAAaA,EAAS,IAAIA,EACL,MAA5BA,EAAOA,EAAO3J,OAAO,KAAa2J,EAASA,EAAOpH,UAAU,EAAEoH,EAAO3J,OAAO,IAC7E2J,GAEJA,EAhFC,MAAMU,EAAgCN,EACH,MAA/BvF,EAAI1G,EAAakC,OAAO,GACO,MAA7BwE,EAAI1G,EAAakC,QAGvB,IAAIiE,EAAQnG,eACJA,EAAakC,OAAS,GACM,IAAxBlC,EAAakC,SAAiBqK,GAEtC,OAAO7F,EAEP,CACA,MAAM8F,EAAM3F,OAAO8E,GACbc,EAAY9F,OAAO6F,GAEzB,GAAY,IAARA,IAAsB,IAATA,EAAY,OAAOA,EACpC,IAAiC,IAA9BC,EAAUnB,OAAO,QAChB,OAAGnF,EAAQlG,UAAkBuM,EACjB9F,EACV,IAAgC,IAA7BiF,EAAW1H,QAAQ,KACxB,MAAiB,MAAdwI,GACKA,IAAcH,GACbG,IAAc,GAAGR,IAAOK,IAFJE,EAGjB9F,EAGhB,IAAIgG,EAAI1M,EAAcsM,EAAoBX,EAC1C,OAAG3L,EAES0M,IAAMD,GAAeR,EAAKS,IAAMD,EAAaD,EAAM9F,EAGnDgG,IAAMD,GAAeC,IAAMT,EAAKQ,EAAaD,EAAM9F,CAEnE,CACJ,CACI,OAAOA,CAEf,CAkCJ,IAAmBmF,CAjCnB,CCugBgBc,CAASvM,EAAK+F,EAC5B,CACE,YJ1jBkB,II0jBN/F,EACHA,EAEA,EAGb,CEzlBA,IAAMkC,EAAkBE,EAAQY,oBAQjB,SAASwJ,EAAS7J,EAAMoD,GACrC,OAAO0G,EAAU9J,EAAMoD,EACzB,CASA,SAAS0G,EAASC,EAAK3G,EAAShF,GAG9B,IAFA,IAAI4L,EACEC,EAAgB,CAAC,EACdzJ,EAAI,EAAGA,EAAIuJ,EAAI5K,OAAQqB,IAAK,CACnC,IAEI0J,EAFEC,EAASJ,EAAIvJ,GACb4J,EAAWC,EAASF,GAK1B,GAHwBD,OAAX9J,IAAVhC,EAAgCgM,EACnBhM,EAAQ,IAAMgM,EAE3BA,IAAahH,EAAQ7G,kBACV6D,IAAT4J,EAAoBA,EAAOG,EAAOC,GAChCJ,GAAQ,GAAKG,EAAOC,OACrB,SAAgBhK,IAAbgK,EACP,SACI,GAAGD,EAAOC,GAAU,CAExB,IAAI/M,EAAMyM,EAASK,EAAOC,GAAWhH,EAAS8G,GACxCI,EAASC,EAAUlN,EAAK+F,QACEhD,IAA5B+J,EAAO5K,KACTlC,EAAIkC,GAAmB4K,EAAO5K,IAG7B4K,EAAO,MACRK,EAAkBnN,EAAK8M,EAAO,MAAOD,EAAU9G,GACZ,IAA5B7H,OAAO4E,KAAK9C,GAAK8B,aAA8CiB,IAA9B/C,EAAI+F,EAAQ7G,eAAgC6G,EAAQ3F,qBAEzD,IAA5BlC,OAAO4E,KAAK9C,GAAK8B,SACrBiE,EAAQ3F,qBAAsBJ,EAAI+F,EAAQ7G,cAAgB,GACxDc,EAAM,IAHXA,EAAMA,EAAI+F,EAAQ7G,mBAMW6D,IAA5B6J,EAAcG,IAA2BH,EAAcnO,eAAesO,IACnE1F,MAAMhH,QAAQuM,EAAcG,MAC5BH,EAAcG,GAAY,CAAEH,EAAcG,KAE9CH,EAAcG,GAAU9K,KAAKjC,IAIzB+F,EAAQ1F,QAAQ0M,EAAUF,EAAUI,GACtCL,EAAcG,GAAY,CAAC/M,GAE3B4M,EAAcG,GAAY/M,CAGhC,EAEF,CAKA,MAHmB,iBAAT2M,EACLA,EAAK7K,OAAS,IAAG8K,EAAc7G,EAAQ7G,cAAgByN,QAC1C5J,IAAT4J,IAAoBC,EAAc7G,EAAQ7G,cAAgByN,GAC5DC,CACT,CAEA,SAASI,EAAS1O,GAEhB,IADA,IAAMwE,EAAO5E,OAAO4E,KAAKxE,GAChB6E,EAAI,EAAGA,EAAIL,EAAKhB,OAAQqB,IAAK,CACpC,IAAMnF,EAAM8E,EAAKK,GACjB,GAAW,OAARnF,EAAc,OAAOA,CAC1B,CACF,CAEA,SAASmP,EAAiB7O,EAAK8O,EAASC,EAAOtH,GAC7C,GAAIqH,EAGF,IAFA,IAAMtK,EAAO5E,OAAO4E,KAAKsK,GACnBrL,EAAMe,EAAKhB,OACRqB,EAAI,EAAGA,EAAIpB,EAAKoB,IAAK,CAC5B,IAAMmK,EAAWxK,EAAKK,GAClB4C,EAAQ1F,QAAQiN,EAAUD,EAAQ,IAAMC,GAAU,GAAM,GAC1DhP,EAAIgP,GAAY,CAAEF,EAAQE,IAE1BhP,EAAIgP,GAAYF,EAAQE,EAE5B,CAEJ,CAEA,SAASJ,EAAU5O,EAAKyH,GACtB,IAAQ7G,EAAiB6G,EAAjB7G,aACFqO,EAAYrP,OAAO4E,KAAKxE,GAAKwD,OAEnC,OAAkB,IAAdyL,KAKY,IAAdA,IACCjP,EAAIY,IAA8C,kBAAtBZ,EAAIY,IAAqD,IAAtBZ,EAAIY,GAMxE,CClHA,IAAMJ,EAAiB,CACrBO,wBAAwB,EACxBkB,aAAc,IA0LhB,SAASiN,EAAaC,GACpB,MAAgB,MAATA,GAAyB,OAATA,GAA0B,OAATA,GAA2B,OAATA,CAC5D,CAMA,SAASC,EAAOxK,EAASC,GAEvB,IADA,IAAMwK,EAAQxK,EACPA,EAAID,EAAQpB,OAAQqB,IACzB,GAAkB,KAAdD,EAAQC,IAA2B,KAAdD,EAAQC,QAAjC,CAEE,IAAMd,EAAUa,EAAQmG,OAAOsE,EAAOxK,EAAIwK,GAC1C,GAAIxK,EAAI,GAAiB,QAAZd,EACX,OAAOuL,EAAe,aAAc,6DAA8DC,EAAyB3K,EAASC,IAC/H,GAAkB,KAAdD,EAAQC,IAA+B,KAAlBD,EAAQC,EAAI,GAAW,CAErDA,IACA,KACF,CAGF,CAEF,OAAOA,CACT,CAEA,SAAS2K,EAAoB5K,EAASC,GACpC,GAAID,EAAQpB,OAASqB,EAAI,GAAwB,MAAnBD,EAAQC,EAAI,IAAiC,MAAnBD,EAAQC,EAAI,IAElE,IAAKA,GAAK,EAAGA,EAAID,EAAQpB,OAAQqB,IAC/B,GAAmB,MAAfD,EAAQC,IAAiC,MAAnBD,EAAQC,EAAI,IAAiC,MAAnBD,EAAQC,EAAI,GAAY,CAC1EA,GAAK,EACL,KACF,OAEG,GACLD,EAAQpB,OAASqB,EAAI,GACF,MAAnBD,EAAQC,EAAI,IACO,MAAnBD,EAAQC,EAAI,IACO,MAAnBD,EAAQC,EAAI,IACO,MAAnBD,EAAQC,EAAI,IACO,MAAnBD,EAAQC,EAAI,IACO,MAAnBD,EAAQC,EAAI,IACO,MAAnBD,EAAQC,EAAI,GACZ,CACA,IAAIG,EAAqB,EACzB,IAAKH,GAAK,EAAGA,EAAID,EAAQpB,OAAQqB,IAC/B,GAAmB,MAAfD,EAAQC,GACVG,SACK,GAAmB,MAAfJ,EAAQC,IAEU,KAD3BG,EAEE,KAIR,MAAO,GACLJ,EAAQpB,OAASqB,EAAI,GACF,MAAnBD,EAAQC,EAAI,IACO,MAAnBD,EAAQC,EAAI,IACO,MAAnBD,EAAQC,EAAI,IACO,MAAnBD,EAAQC,EAAI,IACO,MAAnBD,EAAQC,EAAI,IACO,MAAnBD,EAAQC,EAAI,IACO,MAAnBD,EAAQC,EAAI,GAEZ,IAAKA,GAAK,EAAGA,EAAID,EAAQpB,OAAQqB,IAC/B,GAAmB,MAAfD,EAAQC,IAAiC,MAAnBD,EAAQC,EAAI,IAAiC,MAAnBD,EAAQC,EAAI,GAAY,CAC1EA,GAAK,EACL,KACF,CAIJ,OAAOA,CACT,CAUA,SAAS4K,EAAiB7K,EAASC,GAIjC,IAHA,IAAIsF,EAAU,GACVvD,EAAY,GACZ8I,GAAY,EACT7K,EAAID,EAAQpB,OAAQqB,IAAK,CAC9B,GAbgB,MAaZD,EAAQC,IAZI,MAYkBD,EAAQC,GACtB,KAAd+B,EACFA,EAAYhC,EAAQC,GACX+B,IAAchC,EAAQC,KAG/B+B,EAAY,SAET,GAAmB,MAAfhC,EAAQC,IACC,KAAd+B,EAAkB,CACpB8I,GAAY,EACZ,KACF,CAEFvF,GAAWvF,EAAQC,EACrB,CACA,MAAkB,KAAd+B,GAIG,CACLrG,MAAO4J,EACPzG,MAAOmB,EACP6K,UAAWA,EAEf,CAKA,IAAMC,EAAoB,IAAI7M,OAAO,0DAA2D,KAIhG,SAAS8M,EAAwBzF,EAAS1C,GAQxC,IAHA,IAAMvE,EAAUH,EAAcoH,EAASwF,GACjCE,EAAY,CAAC,EAEVhL,EAAI,EAAGA,EAAI3B,EAAQM,OAAQqB,IAAK,CACvC,GAA6B,IAAzB3B,EAAQ2B,GAAG,GAAGrB,OAEhB,OAAO8L,EAAe,cAAe,cAAcpM,EAAQ2B,GAAG,GAAG,8BAA+BiL,GAAqB5M,EAAQ2B,KACxH,QAAsBJ,IAAlBvB,EAAQ2B,GAAG,SAAsCJ,IAAlBvB,EAAQ2B,GAAG,GACnD,OAAOyK,EAAe,cAAe,cAAcpM,EAAQ2B,GAAG,GAAG,sBAAuBiL,GAAqB5M,EAAQ2B,KAChH,QAAsBJ,IAAlBvB,EAAQ2B,GAAG,KAAqB4C,EAAQ1G,uBAEjD,OAAOuO,EAAe,cAAe,sBAAsBpM,EAAQ2B,GAAG,GAAG,oBAAqBiL,GAAqB5M,EAAQ2B,KAK7H,IAAMjD,EAAWsB,EAAQ2B,GAAG,GAC5B,IAAKkL,EAAiBnO,GACpB,OAAO0N,EAAe,cAAe,cAAc1N,EAAS,wBAAyBkO,GAAqB5M,EAAQ2B,KAEpH,GAAKgL,EAAU1P,eAAeyB,GAI5B,OAAO0N,EAAe,cAAe,cAAc1N,EAAS,iBAAkBkO,GAAqB5M,EAAQ2B,KAF3GgL,EAAUjO,GAAY,CAI1B,CAEA,OAAO,CACT,CAiBA,SAASoO,EAAkBpL,EAASC,GAGlC,GAAmB,MAAfD,IADJC,GAEE,OAAQ,EACV,GAAmB,MAAfD,EAAQC,GAEV,OAtBJ,SAAiCD,EAASC,GACxC,IAAIoL,EAAK,KAKT,IAJmB,MAAfrL,EAAQC,KACVA,IACAoL,EAAK,cAEApL,EAAID,EAAQpB,OAAQqB,IAAK,CAC9B,GAAmB,MAAfD,EAAQC,GACV,OAAOA,EACT,IAAKD,EAAQC,GAAG1B,MAAM8M,GACpB,KACJ,CACA,OAAQ,CACV,CASWC,CAAwBtL,IAD/BC,GAIF,IADA,IAAIsL,EAAQ,EACLtL,EAAID,EAAQpB,OAAQqB,IAAKsL,IAC9B,KAAIvL,EAAQC,GAAG1B,MAAM,OAASgN,EAAQ,IAAtC,CAEA,GAAmB,MAAfvL,EAAQC,GACV,MACF,OAAQ,CAHE,CAKZ,OAAOA,CACT,CAEA,SAASyK,EAAec,EAAMC,EAASC,GACrC,MAAO,CACLC,IAAK,CACHH,KAAMA,EACNI,IAAKH,EACLI,KAAMH,EAAWG,MAAQH,EACzBI,IAAKJ,EAAWI,KAGtB,CAEA,SAASX,EAAiBnO,GACxB,OAAOiC,EAAOjC,EAChB,CASA,SAAS2N,EAAyB3K,EAASlB,GACzC,IAAMiN,EAAQ/L,EAAQmB,UAAU,EAAGrC,GAAOqG,MAAM,SAChD,MAAO,CACL0G,KAAME,EAAMnN,OAGZkN,IAAKC,EAAMA,EAAMnN,OAAS,GAAGA,OAAS,EAE1C,CAGA,SAASsM,GAAqB3M,GAC5B,OAAOA,EAAMG,WAAaH,EAAM,GAAGK,MACrC,CCpamC,IAEdoN,GAAS,WAE1B,SAAAA,EAAYnJ,GACRlI,KAAK8J,iBAAmB,CAAC,EACzB9J,KAAKkI,QTiCe,SAASA,GACjC,OAAO7H,OAAOoN,OAAO,CAAC,EAAGxM,EAAgBiH,EAC7C,CSnCuBoJ,CAAapJ,EAEhC,CACA,IAAAxD,EAAA2M,EAAA1Q,UAwDC,OAxDD+D,EAKA6M,MAAA,SAAMlM,EAAQmM,GACV,GAAsB,iBAAZnM,OACJ,KAAIA,EAAQoM,SAGd,MAAM,IAAIjM,MAAM,mDAFhBH,EAAUA,EAAQoM,UAGtB,CACA,GAAID,EAAiB,EACO,IAArBA,IAA2BA,EAAmB,CAAC,GAElD,IAAMpF,EDlBX,SAAkB/G,EAAS6C,GAChCA,EAAU7H,OAAOoN,OAAO,CAAC,EAAGxM,EAAgBiH,GAK5C,IAAMqC,EAAO,GACTmH,GAAW,EAGXC,GAAc,EAEC,WAAftM,EAAQ,KAEVA,EAAUA,EAAQmG,OAAO,IAG3B,IAAK,IAAIlG,EAAI,EAAGA,EAAID,EAAQpB,OAAQqB,IAElC,GAAmB,MAAfD,EAAQC,IAA+B,MAAjBD,EAAQC,EAAE,IAGlC,IADAA,EAAIuK,EAAOxK,EADXC,GAAG,IAEG0L,IAAK,OAAO1L,MACd,IAAmB,MAAfD,EAAQC,GA0IX,CACL,GAAKqK,EAAatK,EAAQC,IACxB,SAEF,OAAOyK,EAAe,cAAe,SAAS1K,EAAQC,GAAG,qBAAsB0K,EAAyB3K,EAASC,GACnH,CA5IE,IAAIsM,EAActM,EAGlB,GAAmB,MAAfD,IAFJC,GAEwB,CACtBA,EAAI2K,EAAoB5K,EAASC,GACjC,QACF,CACE,IAAIuM,GAAa,EACE,MAAfxM,EAAQC,KAEVuM,GAAa,EACbvM,KAIF,IADA,IAAIpD,EAAU,GACPoD,EAAID,EAAQpB,QACF,MAAfoB,EAAQC,IACO,MAAfD,EAAQC,IACO,OAAfD,EAAQC,IACO,OAAfD,EAAQC,IACO,OAAfD,EAAQC,GAAaA,IAErBpD,GAAWmD,EAAQC,GAWrB,GANoC,OAHpCpD,EAAUA,EAAQsF,QAGNtF,EAAQ+B,OAAS,KAE3B/B,EAAUA,EAAQsE,UAAU,EAAGtE,EAAQ+B,OAAS,GAEhDqB,MAoVDhB,EAlVoBpC,GAOnB,OAAO6N,EAAe,aALQ,IAA1B7N,EAAQsF,OAAOvD,OACX,2BAEA,QAAQ/B,EAAQ,wBAEiB8N,EAAyB3K,EAASC,IAG7E,IAAM8G,EAAS8D,EAAiB7K,EAASC,GACzC,IAAe,IAAX8G,EACF,OAAO2D,EAAe,cAAe,mBAAmB7N,EAAQ,qBAAsB8N,EAAyB3K,EAASC,IAE1H,IAAIsF,EAAUwB,EAAOpL,MAGrB,GAFAsE,EAAI8G,EAAOjI,MAEyB,MAAhCyG,EAAQA,EAAQ3G,OAAS,GAAY,CAEvC,IAAM6N,EAAexM,EAAIsF,EAAQ3G,OAE3B8N,EAAU1B,EADhBzF,EAAUA,EAAQpE,UAAU,EAAGoE,EAAQ3G,OAAS,GACCiE,GACjD,IAAgB,IAAZ6J,EAOF,OAAOhC,EAAegC,EAAQf,IAAIH,KAAMkB,EAAQf,IAAIC,IAAKjB,EAAyB3K,EAASyM,EAAeC,EAAQf,IAAIE,OANtHQ,GAAW,CAQf,MAAO,GAAIG,EAAY,CACrB,IAAKzF,EAAO+D,UACV,OAAOJ,EAAe,aAAc,gBAAgB7N,EAAQ,iCAAkC8N,EAAyB3K,EAASC,IAC3H,GAAIsF,EAAQpD,OAAOvD,OAAS,EACjC,OAAO8L,EAAe,aAAc,gBAAgB7N,EAAQ,+CAAgD8N,EAAyB3K,EAASuM,IACzI,GAAoB,IAAhBrH,EAAKtG,OACd,OAAO8L,EAAe,aAAc,gBAAgB7N,EAAQ,yBAA0B8N,EAAyB3K,EAASuM,IAExH,IAAMI,EAAMzH,EAAKqB,MACjB,GAAI1J,IAAY8P,EAAI9P,QAAS,CAC3B,IAAI+P,EAAUjC,EAAyB3K,EAAS2M,EAAIJ,aACpD,OAAO7B,EAAe,aACpB,yBAAyBiC,EAAI9P,QAAQ,qBAAqB+P,EAAQf,KAAK,SAASe,EAAQd,IAAI,6BAA6BjP,EAAQ,KACjI8N,EAAyB3K,EAASuM,GACtC,CAGmB,GAAfrH,EAAKtG,SACP0N,GAAc,EAGpB,KAAO,CACL,IAAMI,EAAU1B,EAAwBzF,EAAS1C,GACjD,IAAgB,IAAZ6J,EAIF,OAAOhC,EAAegC,EAAQf,IAAIH,KAAMkB,EAAQf,IAAIC,IAAKjB,EAAyB3K,EAASC,EAAIsF,EAAQ3G,OAAS8N,EAAQf,IAAIE,OAI9H,IAAoB,IAAhBS,EACF,OAAO5B,EAAe,aAAc,sCAAuCC,EAAyB3K,EAASC,KAC1D,IAA3C4C,EAAQxF,aAAasD,QAAQ9D,IAGrCqI,EAAKnG,KAAK,CAAClC,QAAAA,EAAS0P,YAAAA,IAEtBF,GAAW,CACb,CAIA,IAAKpM,IAAKA,EAAID,EAAQpB,OAAQqB,IAC5B,GAAmB,MAAfD,EAAQC,GAAY,CACtB,GAAuB,MAAnBD,EAAQC,EAAI,GAAY,CAG1BA,EAAI2K,EAAoB5K,IADxBC,GAEA,QACF,CAAO,GAAqB,MAAjBD,EAAQC,EAAE,GAInB,MAFA,IADAA,EAAIuK,EAAOxK,IAAWC,IAChB0L,IAAK,OAAO1L,CAItB,MAAO,GAAmB,MAAfD,EAAQC,GAAY,CAC7B,IAAM4M,EAAWzB,EAAkBpL,EAASC,GAC5C,IAAiB,GAAb4M,EACF,OAAOnC,EAAe,cAAe,4BAA6BC,EAAyB3K,EAASC,IACtGA,EAAI4M,CACN,MACE,IAAoB,IAAhBP,IAAyBhC,EAAatK,EAAQC,IAChD,OAAOyK,EAAe,aAAc,wBAAyBC,EAAyB3K,EAASC,IAIlF,MAAfD,EAAQC,IACVA,GAQN,CAGF,OAAKoM,EAEoB,GAAfnH,EAAKtG,OACJ8L,EAAe,aAAc,iBAAiBxF,EAAK,GAAGrI,QAAQ,KAAM8N,EAAyB3K,EAASkF,EAAK,GAAGqH,gBAC/GrH,EAAKtG,OAAS,IACb8L,EAAe,aAAc,YAChCoC,KAAKC,UAAU7H,EAAK8H,KAAI,SAAAC,GAAC,OAAIA,EAAEpQ,OAAO,IAAG,KAAM,GAAG+I,QAAQ,SAAU,IACpE,WAAY,CAACiG,KAAM,EAAGC,IAAK,IAN1BpB,EAAe,aAAc,sBAAuB,EAU/D,CClK2BwC,CAASlN,EAASmM,GACjC,IAAe,IAAXpF,EACF,MAAM5G,MAAU4G,EAAO4E,IAAIC,IAAG,IAAI7E,EAAO4E,IAAIE,KAAI,IAAI9E,EAAO4E,IAAIG,IAEpE,CACF,IAAMqB,EAAmB,IAAIvK,EAAiBjI,KAAKkI,SACnDsK,EAAiB1J,oBAAoB9I,KAAK8J,kBAC1C,IAAM2I,EAAgBD,EAAiBzJ,SAAS1D,GAChD,OAAGrF,KAAKkI,QAAQhH,oBAAmCgE,IAAlBuN,EAAoCA,EACzD9D,EAAS8D,EAAezS,KAAKkI,QAC7C,EAEAxD,EAKAgO,UAAA,SAAUvS,EAAKa,GACX,IAA2B,IAAxBA,EAAMgF,QAAQ,KACb,MAAM,IAAIR,MAAM,+BACd,IAAyB,IAAtBrF,EAAI6F,QAAQ,OAAqC,IAAtB7F,EAAI6F,QAAQ,KAC5C,MAAM,IAAIR,MAAM,wEACd,GAAa,MAAVxE,EACL,MAAM,IAAIwE,MAAM,6CAEhBxF,KAAK8J,iBAAiB3J,GAAOa,CAErC,EAEAqQ,EAUOlM,kBAAP,WACI,OAAOZ,EAAQY,mBACnB,EAACkM,CAAA,CA/DyB,G", "sources": ["webpack://XMLParser/webpack/universalModuleDefinition", "webpack://XMLParser/webpack/bootstrap", "webpack://XMLParser/webpack/runtime/define property getters", "webpack://XMLParser/webpack/runtime/hasOwnProperty shorthand", "webpack://XMLParser/webpack/runtime/make namespace object", "webpack://XMLParser/./src/xmlparser/OptionsBuilder.js", "webpack://XMLParser/./src/util.js", "webpack://XMLParser/./src/xmlparser/xmlNode.js", "webpack://XMLParser/./src/xmlparser/DocTypeReader.js", "webpack://XMLParser/./node_modules/strnum/strnum.js", "webpack://XMLParser/./src/xmlparser/OrderedObjParser.js", "webpack://XMLParser/./src/ignoreAttributes.js", "webpack://XMLParser/./src/xmlparser/node2json.js", "webpack://XMLParser/./src/validator.js", "webpack://XMLParser/./src/xmlparser/XMLParser.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"XMLParser\"] = factory();\n\telse\n\t\troot[\"XMLParser\"] = factory();\n})(this, () => {\nreturn ", "// The require scope\nvar __webpack_require__ = {};\n\n", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "\nexport const defaultOptions = {\n    preserveOrder: false,\n    attributeNamePrefix: '@_',\n    attributesGroupName: false,\n    textNodeName: '#text',\n    ignoreAttributes: true,\n    removeNSPrefix: false, // remove NS from tag name or attribute name if true\n    allowBooleanAttributes: false, //a tag can have attributes without any value\n    //ignoreRootElement : false,\n    parseTagValue: true,\n    parseAttributeValue: false,\n    trimValues: true, //Trim string values of tag and attributes\n    cdataPropName: false,\n    numberParseOptions: {\n      hex: true,\n      leadingZeros: true,\n      eNotation: true\n    },\n    tagValueProcessor: function(tagName, val) {\n      return val;\n    },\n    attributeValueProcessor: function(attrName, val) {\n      return val;\n    },\n    stopNodes: [], //nested tags will not be parsed even for errors\n    alwaysCreateTextNode: false,\n    isArray: () => false,\n    commentPropName: false,\n    unpairedTags: [],\n    processEntities: true,\n    htmlEntities: false,\n    ignoreDeclaration: false,\n    ignorePiTags: false,\n    transformTagName: false,\n    transformAttributeName: false,\n    updateTag: function(tagName, jPath, attrs){\n      return tagName\n    },\n    // skipEmptyListItem: false\n    captureMetaData: false,\n};\n   \nexport const buildOptions = function(options) {\n    return Object.assign({}, defaultOptions, options);\n};\n", "'use strict';\n\nconst nameStartChar = ':A-Za-z_\\\\u00C0-\\\\u00D6\\\\u00D8-\\\\u00F6\\\\u00F8-\\\\u02FF\\\\u0370-\\\\u037D\\\\u037F-\\\\u1FFF\\\\u200C-\\\\u200D\\\\u2070-\\\\u218F\\\\u2C00-\\\\u2FEF\\\\u3001-\\\\uD7FF\\\\uF900-\\\\uFDCF\\\\uFDF0-\\\\uFFFD';\nconst nameChar = nameStartChar + '\\\\-.\\\\d\\\\u00B7\\\\u0300-\\\\u036F\\\\u203F-\\\\u2040';\nexport const nameRegexp = '[' + nameStartChar + '][' + nameChar + ']*';\nconst regexName = new RegExp('^' + nameRegexp + '$');\n\nexport function getAllMatches(string, regex) {\n  const matches = [];\n  let match = regex.exec(string);\n  while (match) {\n    const allmatches = [];\n    allmatches.startIndex = regex.lastIndex - match[0].length;\n    const len = match.length;\n    for (let index = 0; index < len; index++) {\n      allmatches.push(match[index]);\n    }\n    matches.push(allmatches);\n    match = regex.exec(string);\n  }\n  return matches;\n}\n\nexport const isName = function(string) {\n  const match = regexName.exec(string);\n  return !(match === null || typeof match === 'undefined');\n}\n\nexport function isExist(v) {\n  return typeof v !== 'undefined';\n}\n\nexport function isEmptyObject(obj) {\n  return Object.keys(obj).length === 0;\n}\n\n/**\n * Copy all the properties of a into b.\n * @param {*} target\n * @param {*} a\n */\nexport function merge(target, a, arrayMode) {\n  if (a) {\n    const keys = Object.keys(a); // will return an array of own properties\n    const len = keys.length; //don't make it inline\n    for (let i = 0; i < len; i++) {\n      if (arrayMode === 'strict') {\n        target[keys[i]] = [ a[keys[i]] ];\n      } else {\n        target[keys[i]] = a[keys[i]];\n      }\n    }\n  }\n}\n/* exports.merge =function (b,a){\n  return Object.assign(b,a);\n} */\n\nexport function getValue(v) {\n  if (exports.isExist(v)) {\n    return v;\n  } else {\n    return '';\n  }\n}\n\n// const fakeCall = function(a) {return a;};\n// const fakeCallNoReturn = function() {};", "'use strict';\n\nlet METADATA_SYMBOL;\n\nif (typeof Symbol !== \"function\") {\n  METADATA_SYMBOL = \"@@xmlMetadata\";\n} else {\n  METADATA_SYMBOL = Symbol(\"XML Node Metadata\");\n}\n\nexport default class XmlNode{\n  constructor(tagname) {\n    this.tagname = tagname;\n    this.child = []; //nested tags, text, cdata, comments in order\n    this[\":@\"] = {}; //attributes map\n  }\n  add(key,val){\n    // this.child.push( {name : key, val: val, isCdata: isCdata });\n    if(key === \"__proto__\") key = \"#__proto__\";\n    this.child.push( {[key]: val });\n  }\n  addChild(node, startIndex) {\n    if(node.tagname === \"__proto__\") node.tagname = \"#__proto__\";\n    if(node[\":@\"] && Object.keys(node[\":@\"]).length > 0){\n      this.child.push( { [node.tagname]: node.child, [\":@\"]: node[\":@\"] });\n    }else{\n      this.child.push( { [node.tagname]: node.child });\n    }\n    // if requested, add the startIndex\n    if (startIndex !== undefined) {\n      // Note: for now we just overwrite the metadata. If we had more complex metadata,\n      // we might need to do an object append here:  metadata = { ...metadata, startIndex }\n      this.child[this.child.length - 1][METADATA_SYMBOL] = { startIndex };\n    }\n  }\n  /** symbol used for metadata */\n  static getMetaDataSymbol() {\n    return METADATA_SYMBOL;\n  }\n}\n", "import {isName} from '../util.js';\n\n//TODO: handle comments\nexport default function readDocType(xmlData, i){\n    \n    const entities = {};\n    if( xmlData[i + 3] === 'O' &&\n         xmlData[i + 4] === 'C' &&\n         xmlData[i + 5] === 'T' &&\n         xmlData[i + 6] === 'Y' &&\n         xmlData[i + 7] === 'P' &&\n         xmlData[i + 8] === 'E')\n    {    \n        i = i+9;\n        let angleBracketsCount = 1;\n        let hasBody = false, comment = false;\n        let exp = \"\";\n        for(;i<xmlData.length;i++){\n            if (xmlData[i] === '<' && !comment) { //Determine the tag type\n                if( hasBody && hasSeq(xmlData, \"!ENTITY\",i)){\n                    i += 7; \n                    let entityName, val;\n                    [entityName, val,i] = readEntityExp(xmlData,i+1);\n                    if(val.indexOf(\"&\") === -1) //Parameter entities are not supported\n                        entities[ entityName ] = {\n                            regx : RegExp( `&${entityName};`,\"g\"),\n                            val: val\n                        };\n                }\n                else if( hasBody && hasSeq(xmlData, \"!ELEMENT\",i))  {\n                    i += 8;//Not supported\n                    const {index} = readElementExp(xmlData,i+1);\n                    i = index;\n                }else if( hasBody && hasSeq(xmlData, \"!ATTLIST\",i)){\n                    i += 8;//Not supported\n                    // const {index} = readAttlistExp(xmlData,i+1);\n                    // i = index;\n                }else if( hasBody && hasSeq(xmlData, \"!NOTATION\",i)) {\n                    i += 9;//Not supported\n                    const {index} = readNotationExp(xmlData,i+1);\n                    i = index;\n                }else if( hasSeq(xmlData, \"!--\",i) ) comment = true;\n                else throw new Error(`Invalid DOCTYPE`);\n\n                angleBracketsCount++;\n                exp = \"\";\n            } else if (xmlData[i] === '>') { //Read tag content\n                if(comment){\n                    if( xmlData[i - 1] === \"-\" && xmlData[i - 2] === \"-\"){\n                        comment = false;\n                        angleBracketsCount--;\n                    }\n                }else{\n                    angleBracketsCount--;\n                }\n                if (angleBracketsCount === 0) {\n                  break;\n                }\n            }else if( xmlData[i] === '['){\n                hasBody = true;\n            }else{\n                exp += xmlData[i];\n            }\n        }\n        if(angleBracketsCount !== 0){\n            throw new Error(`Unclosed DOCTYPE`);\n        }\n    }else{\n        throw new Error(`Invalid Tag instead of DOCTYPE`);\n    }\n    return {entities, i};\n}\n\nconst skipWhitespace = (data, index) => {\n    while (index < data.length && /\\s/.test(data[index])) {\n        index++;\n    }\n    return index;\n};\n\nfunction readEntityExp(xmlData, i) {    \n    //External entities are not supported\n    //    <!ENTITY ext SYSTEM \"http://normal-website.com\" >\n\n    //Parameter entities are not supported\n    //    <!ENTITY entityname \"&anotherElement;\">\n\n    //Internal entities are supported\n    //    <!ENTITY entityname \"replacement text\">\n\n    // Skip leading whitespace after <!ENTITY\n    i = skipWhitespace(xmlData, i);\n\n    // Read entity name\n    let entityName = \"\";\n    while (i < xmlData.length && !/\\s/.test(xmlData[i]) && xmlData[i] !== '\"' && xmlData[i] !== \"'\") {\n        entityName += xmlData[i];\n        i++;\n    }\n    validateEntityName(entityName);\n\n    // Skip whitespace after entity name\n    i = skipWhitespace(xmlData, i);\n\n    // Check for unsupported constructs (external entities or parameter entities)\n    if (xmlData.substring(i, i + 6).toUpperCase() === \"SYSTEM\") {\n        throw new Error(\"External entities are not supported\");\n    }else if (xmlData[i] === \"%\") {\n        throw new Error(\"Parameter entities are not supported\");\n    }\n\n    // Read entity value (internal entity)\n    let entityValue = \"\";\n    [i, entityValue] = readIdentifierVal(xmlData, i, \"entity\");\n    i--;\n    return [entityName, entityValue, i ];\n}\n\nfunction readNotationExp(xmlData, i) {\n    // Skip leading whitespace after <!NOTATION\n    i = skipWhitespace(xmlData, i);\n\n    // Read notation name\n    let notationName = \"\";\n    while (i < xmlData.length && !/\\s/.test(xmlData[i])) {\n        notationName += xmlData[i];\n        i++;\n    }\n    validateEntityName(notationName);\n\n    // Skip whitespace after notation name\n    i = skipWhitespace(xmlData, i);\n\n    // Check identifier type (SYSTEM or PUBLIC)\n    const identifierType = xmlData.substring(i, i + 6).toUpperCase();\n    if (identifierType !== \"SYSTEM\" && identifierType !== \"PUBLIC\") {\n        throw new Error(`Expected SYSTEM or PUBLIC, found \"${identifierType}\"`);\n    }\n    i += identifierType.length;\n\n    // Skip whitespace after identifier type\n    i = skipWhitespace(xmlData, i);\n\n    // Read public identifier (if PUBLIC)\n    let publicIdentifier = null;\n    let systemIdentifier = null;\n\n    if (identifierType === \"PUBLIC\") {\n        [i, publicIdentifier ] = readIdentifierVal(xmlData, i, \"publicIdentifier\");\n\n        // Skip whitespace after public identifier\n        i = skipWhitespace(xmlData, i);\n\n        // Optionally read system identifier\n        if (xmlData[i] === '\"' || xmlData[i] === \"'\") {\n            [i, systemIdentifier ] = readIdentifierVal(xmlData, i,\"systemIdentifier\");\n        }\n    } else if (identifierType === \"SYSTEM\") {\n        // Read system identifier (mandatory for SYSTEM)\n        [i, systemIdentifier ] = readIdentifierVal(xmlData, i, \"systemIdentifier\");\n\n        if (!systemIdentifier) {\n            throw new Error(\"Missing mandatory system identifier for SYSTEM notation\");\n        }\n    }\n    \n    return {notationName, publicIdentifier, systemIdentifier, index: --i};\n}\n\nfunction readIdentifierVal(xmlData, i, type) {\n    let identifierVal = \"\";\n    const startChar = xmlData[i];\n    if (startChar !== '\"' && startChar !== \"'\") {\n        throw new Error(`Expected quoted string, found \"${startChar}\"`);\n    }\n    i++;\n\n    while (i < xmlData.length && xmlData[i] !== startChar) {\n        identifierVal += xmlData[i];\n        i++;\n    }\n\n    if (xmlData[i] !== startChar) {\n        throw new Error(`Unterminated ${type} value`);\n    }\n    i++;\n    return [i, identifierVal];\n}\n\nfunction readElementExp(xmlData, i) {\n    // <!ELEMENT br EMPTY>\n    // <!ELEMENT div ANY>\n    // <!ELEMENT title (#PCDATA)>\n    // <!ELEMENT book (title, author+)>\n    // <!ELEMENT name (content-model)>\n    \n    // Skip leading whitespace after <!ELEMENT\n    i = skipWhitespace(xmlData, i);\n\n    // Read element name\n    let elementName = \"\";\n    while (i < xmlData.length && !/\\s/.test(xmlData[i])) {\n        elementName += xmlData[i];\n        i++;\n    }\n\n    // Validate element name\n    if (!validateEntityName(elementName)) {\n        throw new Error(`Invalid element name: \"${elementName}\"`);\n    }\n\n    // Skip whitespace after element name\n    i = skipWhitespace(xmlData, i);\n    let contentModel = \"\";\n    // Expect '(' to start content model\n    if(xmlData[i] === \"E\" && hasSeq(xmlData, \"MPTY\",i)) i+=4;\n    else if(xmlData[i] === \"A\" && hasSeq(xmlData, \"NY\",i)) i+=2;\n    else if (xmlData[i] === \"(\") {\n        i++; // Move past '('\n\n        // Read content model\n        while (i < xmlData.length && xmlData[i] !== \")\") {\n            contentModel += xmlData[i];\n            i++;\n        }\n        if (xmlData[i] !== \")\") {\n            throw new Error(\"Unterminated content model\");\n        }\n\n    }else{\n        throw new Error(`Invalid Element Expression, found \"${xmlData[i]}\"`);\n    }\n    \n    return {\n        elementName,\n        contentModel: contentModel.trim(),\n        index: i\n    };\n}\n\nfunction readAttlistExp(xmlData, i) {\n    // Skip leading whitespace after <!ATTLIST\n    i = skipWhitespace(xmlData, i);\n\n    // Read element name\n    let elementName = \"\";\n    while (i < xmlData.length && !/\\s/.test(xmlData[i])) {\n        elementName += xmlData[i];\n        i++;\n    }\n\n    // Validate element name\n    validateEntityName(elementName)\n\n    // Skip whitespace after element name\n    i = skipWhitespace(xmlData, i);\n\n    // Read attribute name\n    let attributeName = \"\";\n    while (i < xmlData.length && !/\\s/.test(xmlData[i])) {\n        attributeName += xmlData[i];\n        i++;\n    }\n\n    // Validate attribute name\n    if (!validateEntityName(attributeName)) {\n        throw new Error(`Invalid attribute name: \"${attributeName}\"`);\n    }\n\n    // Skip whitespace after attribute name\n    i = skipWhitespace(xmlData, i);\n\n    // Read attribute type\n    let attributeType = \"\";\n    if (xmlData.substring(i, i + 8).toUpperCase() === \"NOTATION\") {\n        attributeType = \"NOTATION\";\n        i += 8; // Move past \"NOTATION\"\n\n        // Skip whitespace after \"NOTATION\"\n        i = skipWhitespace(xmlData, i);\n\n        // Expect '(' to start the list of notations\n        if (xmlData[i] !== \"(\") {\n            throw new Error(`Expected '(', found \"${xmlData[i]}\"`);\n        }\n        i++; // Move past '('\n\n        // Read the list of allowed notations\n        let allowedNotations = [];\n        while (i < xmlData.length && xmlData[i] !== \")\") {\n            let notation = \"\";\n            while (i < xmlData.length && xmlData[i] !== \"|\" && xmlData[i] !== \")\") {\n                notation += xmlData[i];\n                i++;\n            }\n\n            // Validate notation name\n            notation = notation.trim();\n            if (!validateEntityName(notation)) {\n                throw new Error(`Invalid notation name: \"${notation}\"`);\n            }\n\n            allowedNotations.push(notation);\n\n            // Skip '|' separator or exit loop\n            if (xmlData[i] === \"|\") {\n                i++; // Move past '|'\n                i = skipWhitespace(xmlData, i); // Skip optional whitespace after '|'\n            }\n        }\n\n        if (xmlData[i] !== \")\") {\n            throw new Error(\"Unterminated list of notations\");\n        }\n        i++; // Move past ')'\n\n        // Store the allowed notations as part of the attribute type\n        attributeType += \" (\" + allowedNotations.join(\"|\") + \")\";\n    } else {\n        // Handle simple types (e.g., CDATA, ID, IDREF, etc.)\n        while (i < xmlData.length && !/\\s/.test(xmlData[i])) {\n            attributeType += xmlData[i];\n            i++;\n        }\n\n        // Validate simple attribute type\n        const validTypes = [\"CDATA\", \"ID\", \"IDREF\", \"IDREFS\", \"ENTITY\", \"ENTITIES\", \"NMTOKEN\", \"NMTOKENS\"];\n        if (!validTypes.includes(attributeType.toUpperCase())) {\n            throw new Error(`Invalid attribute type: \"${attributeType}\"`);\n        }\n    }\n\n    // Skip whitespace after attribute type\n    i = skipWhitespace(xmlData, i);\n\n    // Read default value\n    let defaultValue = \"\";\n    if (xmlData.substring(i, i + 8).toUpperCase() === \"#REQUIRED\") {\n        defaultValue = \"#REQUIRED\";\n        i += 8;\n    } else if (xmlData.substring(i, i + 7).toUpperCase() === \"#IMPLIED\") {\n        defaultValue = \"#IMPLIED\";\n        i += 7;\n    } else {\n        [i, defaultValue] = readIdentifierVal(xmlData, i, \"ATTLIST\");\n    }\n\n    return {\n        elementName,\n        attributeName,\n        attributeType,\n        defaultValue,\n        index: i\n    }\n}\n\nfunction hasSeq(data, seq,i){\n    for(let j=0;j<seq.length;j++){\n        if(seq[j]!==data[i+j+1]) return false;\n    }\n    return true;\n}\n\nfunction validateEntityName(name){\n    if (isName(name))\n\treturn name;\n    else\n        throw new Error(`Invalid entity name ${name}`);\n}\n", "const hexRegex = /^[-+]?0x[a-fA-F0-9]+$/;\nconst numRegex = /^([\\-\\+])?(0*)([0-9]*(\\.[0-9]*)?)$/;\n// const octRegex = /^0x[a-z0-9]+/;\n// const binRegex = /0x[a-z0-9]+/;\n\n \nconst consider = {\n    hex :  true,\n    // oct: false,\n    leadingZeros: true,\n    decimalPoint: \"\\.\",\n    eNotation: true,\n    //skipLike: /regex/\n};\n\nexport default function toNumber(str, options = {}){\n    options = Object.assign({}, consider, options );\n    if(!str || typeof str !== \"string\" ) return str;\n    \n    let trimmedStr  = str.trim();\n    \n    if(options.skipLike !== undefined && options.skipLike.test(trimmedStr)) return str;\n    else if(str===\"0\") return 0;\n    else if (options.hex && hexRegex.test(trimmedStr)) {\n        return parse_int(trimmedStr, 16);\n    // }else if (options.oct && octRegex.test(str)) {\n    //     return Number.parseInt(val, 8);\n    }else if (trimmedStr.search(/.+[eE].+/)!== -1) { //eNotation\n        return resolveEnotation(str,trimmedStr,options);\n    // }else if (options.parseBin && binRegex.test(str)) {\n    //     return Number.parseInt(val, 2);\n    }else{\n        //separate negative sign, leading zeros, and rest number\n        const match = numRegex.exec(trimmedStr);\n        // +00.123 => [ , '+', '00', '.123', ..\n        if(match){\n            const sign = match[1] || \"\";\n            const leadingZeros = match[2];\n            let numTrimmedByZeros = trimZeros(match[3]); //complete num without leading zeros\n            const decimalAdjacentToLeadingZeros = sign ? // 0., -00., 000.\n                str[leadingZeros.length+1] === \".\" \n                : str[leadingZeros.length] === \".\";\n\n            //trim ending zeros for floating number\n            if(!options.leadingZeros //leading zeros are not allowed\n                && (leadingZeros.length > 1 \n                    || (leadingZeros.length === 1 && !decimalAdjacentToLeadingZeros))){\n                // 00, 00.3, +03.24, 03, 03.24\n                return str;\n            }\n            else{//no leading zeros or leading zeros are allowed\n                const num = Number(trimmedStr);\n                const parsedStr = String(num);\n\n                if( num === 0 || num === -0) return num;\n                if(parsedStr.search(/[eE]/) !== -1){ //given number is long and parsed to eNotation\n                    if(options.eNotation) return num;\n                    else return str;\n                }else if(trimmedStr.indexOf(\".\") !== -1){ //floating number\n                    if(parsedStr === \"0\") return num; //0.0\n                    else if(parsedStr === numTrimmedByZeros) return num; //0.456. 0.79000\n                    else if( parsedStr === `${sign}${numTrimmedByZeros}`) return num;\n                    else return str;\n                }\n                \n                let n = leadingZeros? numTrimmedByZeros : trimmedStr;\n                if(leadingZeros){\n                    // -009 => -9\n                    return (n === parsedStr) || (sign+n === parsedStr) ? num : str\n                }else  {\n                    // +9\n                    return (n === parsedStr) || (n === sign+parsedStr) ? num : str\n                }\n            }\n        }else{ //non-numeric string\n            return str;\n        }\n    }\n}\n\nconst eNotationRegx = /^([-+])?(0*)(\\d*(\\.\\d*)?[eE][-\\+]?\\d+)$/;\nfunction resolveEnotation(str,trimmedStr,options){\n    if(!options.eNotation) return str;\n    const notation = trimmedStr.match(eNotationRegx); \n    if(notation){\n        let sign = notation[1] || \"\";\n        const eChar = notation[3].indexOf(\"e\") === -1 ? \"E\" : \"e\";\n        const leadingZeros = notation[2];\n        const eAdjacentToLeadingZeros = sign ? // 0E.\n            str[leadingZeros.length+1] === eChar \n            : str[leadingZeros.length] === eChar;\n\n        if(leadingZeros.length > 1 && eAdjacentToLeadingZeros) return str;\n        else if(leadingZeros.length === 1 \n            && (notation[3].startsWith(`.${eChar}`) || notation[3][0] === eChar)){\n                return Number(trimmedStr);\n        }else if(options.leadingZeros && !eAdjacentToLeadingZeros){ //accept with leading zeros\n            //remove leading 0s\n            trimmedStr = (notation[1] || \"\") + notation[3];\n            return Number(trimmedStr);\n        }else return str;\n    }else{\n        return str;\n    }\n}\n\n/**\n * \n * @param {string} numStr without leading zeros\n * @returns \n */\nfunction trimZeros(numStr){\n    if(numStr && numStr.indexOf(\".\") !== -1){//float\n        numStr = numStr.replace(/0+$/, \"\"); //remove ending zeros\n        if(numStr === \".\")  numStr = \"0\";\n        else if(numStr[0] === \".\")  numStr = \"0\"+numStr;\n        else if(numStr[numStr.length-1] === \".\")  numStr = numStr.substring(0,numStr.length-1);\n        return numStr;\n    }\n    return numStr;\n}\n\nfunction parse_int(numStr, base){\n    //polyfill\n    if(parseInt) return parseInt(numStr, base);\n    else if(Number.parseInt) return Number.parseInt(numStr, base);\n    else if(window && window.parseInt) return window.parseInt(numStr, base);\n    else throw new Error(\"parseInt, Number.parseInt, window.parseInt are not supported\")\n}", "'use strict';\n///@ts-check\n\nimport {getAllMatches, isExist} from '../util.js';\nimport xmlNode from './xmlNode.js';\nimport readDocType from './DocTypeReader.js';\nimport toNumber from \"strnum\";\nimport getIgnoreAttributesFn from \"../ignoreAttributes.js\";\n\n// const regx =\n//   '<((!\\\\[CDATA\\\\[([\\\\s\\\\S]*?)(]]>))|((NAME:)?(NAME))([^>]*)>|((\\\\/)(NAME)\\\\s*>))([^<]*)'\n//   .replace(/NAME/g, util.nameRegexp);\n\n//const tagsRegx = new RegExp(\"<(\\\\/?[\\\\w:\\\\-\\._]+)([^>]*)>(\\\\s*\"+cdataRegx+\")*([^<]+)?\",\"g\");\n//const tagsRegx = new RegExp(\"<(\\\\/?)((\\\\w*:)?([\\\\w:\\\\-\\._]+))([^>]*)>([^<]*)(\"+cdataRegx+\"([^<]*))*([^<]+)?\",\"g\");\n\nexport default class OrderedObjParser{\n  constructor(options){\n    this.options = options;\n    this.currentNode = null;\n    this.tagsNodeStack = [];\n    this.docTypeEntities = {};\n    this.lastEntities = {\n      \"apos\" : { regex: /&(apos|#39|#x27);/g, val : \"'\"},\n      \"gt\" : { regex: /&(gt|#62|#x3E);/g, val : \">\"},\n      \"lt\" : { regex: /&(lt|#60|#x3C);/g, val : \"<\"},\n      \"quot\" : { regex: /&(quot|#34|#x22);/g, val : \"\\\"\"},\n    };\n    this.ampEntity = { regex: /&(amp|#38|#x26);/g, val : \"&\"};\n    this.htmlEntities = {\n      \"space\": { regex: /&(nbsp|#160);/g, val: \" \" },\n      // \"lt\" : { regex: /&(lt|#60);/g, val: \"<\" },\n      // \"gt\" : { regex: /&(gt|#62);/g, val: \">\" },\n      // \"amp\" : { regex: /&(amp|#38);/g, val: \"&\" },\n      // \"quot\" : { regex: /&(quot|#34);/g, val: \"\\\"\" },\n      // \"apos\" : { regex: /&(apos|#39);/g, val: \"'\" },\n      \"cent\" : { regex: /&(cent|#162);/g, val: \"¢\" },\n      \"pound\" : { regex: /&(pound|#163);/g, val: \"£\" },\n      \"yen\" : { regex: /&(yen|#165);/g, val: \"¥\" },\n      \"euro\" : { regex: /&(euro|#8364);/g, val: \"€\" },\n      \"copyright\" : { regex: /&(copy|#169);/g, val: \"©\" },\n      \"reg\" : { regex: /&(reg|#174);/g, val: \"®\" },\n      \"inr\" : { regex: /&(inr|#8377);/g, val: \"₹\" },\n      \"num_dec\": { regex: /&#([0-9]{1,7});/g, val : (_, str) => String.fromCodePoint(Number.parseInt(str, 10)) },\n      \"num_hex\": { regex: /&#x([0-9a-fA-F]{1,6});/g, val : (_, str) => String.fromCodePoint(Number.parseInt(str, 16)) },\n    };\n    this.addExternalEntities = addExternalEntities;\n    this.parseXml = parseXml;\n    this.parseTextData = parseTextData;\n    this.resolveNameSpace = resolveNameSpace;\n    this.buildAttributesMap = buildAttributesMap;\n    this.isItStopNode = isItStopNode;\n    this.replaceEntitiesValue = replaceEntitiesValue;\n    this.readStopNodeData = readStopNodeData;\n    this.saveTextToParentTag = saveTextToParentTag;\n    this.addChild = addChild;\n    this.ignoreAttributesFn = getIgnoreAttributesFn(this.options.ignoreAttributes)\n  }\n\n}\n\nfunction addExternalEntities(externalEntities){\n  const entKeys = Object.keys(externalEntities);\n  for (let i = 0; i < entKeys.length; i++) {\n    const ent = entKeys[i];\n    this.lastEntities[ent] = {\n       regex: new RegExp(\"&\"+ent+\";\",\"g\"),\n       val : externalEntities[ent]\n    }\n  }\n}\n\n/**\n * @param {string} val\n * @param {string} tagName\n * @param {string} jPath\n * @param {boolean} dontTrim\n * @param {boolean} hasAttributes\n * @param {boolean} isLeafNode\n * @param {boolean} escapeEntities\n */\nfunction parseTextData(val, tagName, jPath, dontTrim, hasAttributes, isLeafNode, escapeEntities) {\n  if (val !== undefined) {\n    if (this.options.trimValues && !dontTrim) {\n      val = val.trim();\n    }\n    if(val.length > 0){\n      if(!escapeEntities) val = this.replaceEntitiesValue(val);\n      \n      const newval = this.options.tagValueProcessor(tagName, val, jPath, hasAttributes, isLeafNode);\n      if(newval === null || newval === undefined){\n        //don't parse\n        return val;\n      }else if(typeof newval !== typeof val || newval !== val){\n        //overwrite\n        return newval;\n      }else if(this.options.trimValues){\n        return parseValue(val, this.options.parseTagValue, this.options.numberParseOptions);\n      }else{\n        const trimmedVal = val.trim();\n        if(trimmedVal === val){\n          return parseValue(val, this.options.parseTagValue, this.options.numberParseOptions);\n        }else{\n          return val;\n        }\n      }\n    }\n  }\n}\n\nfunction resolveNameSpace(tagname) {\n  if (this.options.removeNSPrefix) {\n    const tags = tagname.split(':');\n    const prefix = tagname.charAt(0) === '/' ? '/' : '';\n    if (tags[0] === 'xmlns') {\n      return '';\n    }\n    if (tags.length === 2) {\n      tagname = prefix + tags[1];\n    }\n  }\n  return tagname;\n}\n\n//TODO: change regex to capture NS\n//const attrsRegx = new RegExp(\"([\\\\w\\\\-\\\\.\\\\:]+)\\\\s*=\\\\s*(['\\\"])((.|\\n)*?)\\\\2\",\"gm\");\nconst attrsRegx = new RegExp('([^\\\\s=]+)\\\\s*(=\\\\s*([\\'\"])([\\\\s\\\\S]*?)\\\\3)?', 'gm');\n\nfunction buildAttributesMap(attrStr, jPath, tagName) {\n  if (this.options.ignoreAttributes !== true && typeof attrStr === 'string') {\n    // attrStr = attrStr.replace(/\\r?\\n/g, ' ');\n    //attrStr = attrStr || attrStr.trim();\n\n    const matches = getAllMatches(attrStr, attrsRegx);\n    const len = matches.length; //don't make it inline\n    const attrs = {};\n    for (let i = 0; i < len; i++) {\n      const attrName = this.resolveNameSpace(matches[i][1]);\n      if (this.ignoreAttributesFn(attrName, jPath)) {\n        continue\n      }\n      let oldVal = matches[i][4];\n      let aName = this.options.attributeNamePrefix + attrName;\n      if (attrName.length) {\n        if (this.options.transformAttributeName) {\n          aName = this.options.transformAttributeName(aName);\n        }\n        if(aName === \"__proto__\") aName  = \"#__proto__\";\n        if (oldVal !== undefined) {\n          if (this.options.trimValues) {\n            oldVal = oldVal.trim();\n          }\n          oldVal = this.replaceEntitiesValue(oldVal);\n          const newVal = this.options.attributeValueProcessor(attrName, oldVal, jPath);\n          if(newVal === null || newVal === undefined){\n            //don't parse\n            attrs[aName] = oldVal;\n          }else if(typeof newVal !== typeof oldVal || newVal !== oldVal){\n            //overwrite\n            attrs[aName] = newVal;\n          }else{\n            //parse\n            attrs[aName] = parseValue(\n              oldVal,\n              this.options.parseAttributeValue,\n              this.options.numberParseOptions\n            );\n          }\n        } else if (this.options.allowBooleanAttributes) {\n          attrs[aName] = true;\n        }\n      }\n    }\n    if (!Object.keys(attrs).length) {\n      return;\n    }\n    if (this.options.attributesGroupName) {\n      const attrCollection = {};\n      attrCollection[this.options.attributesGroupName] = attrs;\n      return attrCollection;\n    }\n    return attrs\n  }\n}\n\nconst parseXml = function(xmlData) {\n  xmlData = xmlData.replace(/\\r\\n?/g, \"\\n\"); //TODO: remove this line\n  const xmlObj = new xmlNode('!xml');\n  let currentNode = xmlObj;\n  let textData = \"\";\n  let jPath = \"\";\n  for(let i=0; i< xmlData.length; i++){//for each char in XML data\n    const ch = xmlData[i];\n    if(ch === '<'){\n      // const nextIndex = i+1;\n      // const _2ndChar = xmlData[nextIndex];\n      if( xmlData[i+1] === '/') {//Closing Tag\n        const closeIndex = findClosingIndex(xmlData, \">\", i, \"Closing Tag is not closed.\")\n        let tagName = xmlData.substring(i+2,closeIndex).trim();\n\n        if(this.options.removeNSPrefix){\n          const colonIndex = tagName.indexOf(\":\");\n          if(colonIndex !== -1){\n            tagName = tagName.substr(colonIndex+1);\n          }\n        }\n\n        if(this.options.transformTagName) {\n          tagName = this.options.transformTagName(tagName);\n        }\n\n        if(currentNode){\n          textData = this.saveTextToParentTag(textData, currentNode, jPath);\n        }\n\n        //check if last tag of nested tag was unpaired tag\n        const lastTagName = jPath.substring(jPath.lastIndexOf(\".\")+1);\n        if(tagName && this.options.unpairedTags.indexOf(tagName) !== -1 ){\n          throw new Error(`Unpaired tag can not be used as closing tag: </${tagName}>`);\n        }\n        let propIndex = 0\n        if(lastTagName && this.options.unpairedTags.indexOf(lastTagName) !== -1 ){\n          propIndex = jPath.lastIndexOf('.', jPath.lastIndexOf('.')-1)\n          this.tagsNodeStack.pop();\n        }else{\n          propIndex = jPath.lastIndexOf(\".\");\n        }\n        jPath = jPath.substring(0, propIndex);\n\n        currentNode = this.tagsNodeStack.pop();//avoid recursion, set the parent tag scope\n        textData = \"\";\n        i = closeIndex;\n      } else if( xmlData[i+1] === '?') {\n\n        let tagData = readTagExp(xmlData,i, false, \"?>\");\n        if(!tagData) throw new Error(\"Pi Tag is not closed.\");\n\n        textData = this.saveTextToParentTag(textData, currentNode, jPath);\n        if( (this.options.ignoreDeclaration && tagData.tagName === \"?xml\") || this.options.ignorePiTags){\n\n        }else{\n  \n          const childNode = new xmlNode(tagData.tagName);\n          childNode.add(this.options.textNodeName, \"\");\n          \n          if(tagData.tagName !== tagData.tagExp && tagData.attrExpPresent){\n            childNode[\":@\"] = this.buildAttributesMap(tagData.tagExp, jPath, tagData.tagName);\n          }\n          this.addChild(currentNode, childNode, jPath, i);\n        }\n\n\n        i = tagData.closeIndex + 1;\n      } else if(xmlData.substr(i + 1, 3) === '!--') {\n        const endIndex = findClosingIndex(xmlData, \"-->\", i+4, \"Comment is not closed.\")\n        if(this.options.commentPropName){\n          const comment = xmlData.substring(i + 4, endIndex - 2);\n\n          textData = this.saveTextToParentTag(textData, currentNode, jPath);\n\n          currentNode.add(this.options.commentPropName, [ { [this.options.textNodeName] : comment } ]);\n        }\n        i = endIndex;\n      } else if( xmlData.substr(i + 1, 2) === '!D') {\n        const result = readDocType(xmlData, i);\n        this.docTypeEntities = result.entities;\n        i = result.i;\n      }else if(xmlData.substr(i + 1, 2) === '![') {\n        const closeIndex = findClosingIndex(xmlData, \"]]>\", i, \"CDATA is not closed.\") - 2;\n        const tagExp = xmlData.substring(i + 9,closeIndex);\n\n        textData = this.saveTextToParentTag(textData, currentNode, jPath);\n\n        let val = this.parseTextData(tagExp, currentNode.tagname, jPath, true, false, true, true);\n        if(val == undefined) val = \"\";\n\n        //cdata should be set even if it is 0 length string\n        if(this.options.cdataPropName){\n          currentNode.add(this.options.cdataPropName, [ { [this.options.textNodeName] : tagExp } ]);\n        }else{\n          currentNode.add(this.options.textNodeName, val);\n        }\n        \n        i = closeIndex + 2;\n      }else {//Opening tag\n        let result = readTagExp(xmlData,i, this.options.removeNSPrefix);\n        let tagName= result.tagName;\n        const rawTagName = result.rawTagName;\n        let tagExp = result.tagExp;\n        let attrExpPresent = result.attrExpPresent;\n        let closeIndex = result.closeIndex;\n\n        if (this.options.transformTagName) {\n          tagName = this.options.transformTagName(tagName);\n        }\n        \n        //save text as child node\n        if (currentNode && textData) {\n          if(currentNode.tagname !== '!xml'){\n            //when nested tag is found\n            textData = this.saveTextToParentTag(textData, currentNode, jPath, false);\n          }\n        }\n\n        //check if last tag was unpaired tag\n        const lastTag = currentNode;\n        if(lastTag && this.options.unpairedTags.indexOf(lastTag.tagname) !== -1 ){\n          currentNode = this.tagsNodeStack.pop();\n          jPath = jPath.substring(0, jPath.lastIndexOf(\".\"));\n        }\n        if(tagName !== xmlObj.tagname){\n          jPath += jPath ? \".\" + tagName : tagName;\n        }\n        const startIndex = i;\n        if (this.isItStopNode(this.options.stopNodes, jPath, tagName)) {\n          let tagContent = \"\";\n          //self-closing tag\n          if(tagExp.length > 0 && tagExp.lastIndexOf(\"/\") === tagExp.length - 1){\n            if(tagName[tagName.length - 1] === \"/\"){ //remove trailing '/'\n              tagName = tagName.substr(0, tagName.length - 1);\n              jPath = jPath.substr(0, jPath.length - 1);\n              tagExp = tagName;\n            }else{\n              tagExp = tagExp.substr(0, tagExp.length - 1);\n            }\n            i = result.closeIndex;\n          }\n          //unpaired tag\n          else if(this.options.unpairedTags.indexOf(tagName) !== -1){\n            \n            i = result.closeIndex;\n          }\n          //normal tag\n          else{\n            //read until closing tag is found\n            const result = this.readStopNodeData(xmlData, rawTagName, closeIndex + 1);\n            if(!result) throw new Error(`Unexpected end of ${rawTagName}`);\n            i = result.i;\n            tagContent = result.tagContent;\n          }\n\n          const childNode = new xmlNode(tagName);\n\n          if(tagName !== tagExp && attrExpPresent){\n            childNode[\":@\"] = this.buildAttributesMap(tagExp, jPath, tagName);\n          }\n          if(tagContent) {\n            tagContent = this.parseTextData(tagContent, tagName, jPath, true, attrExpPresent, true, true);\n          }\n          \n          jPath = jPath.substr(0, jPath.lastIndexOf(\".\"));\n          childNode.add(this.options.textNodeName, tagContent);\n          \n          this.addChild(currentNode, childNode, jPath, startIndex);\n        }else{\n  //selfClosing tag\n          if(tagExp.length > 0 && tagExp.lastIndexOf(\"/\") === tagExp.length - 1){\n            if(tagName[tagName.length - 1] === \"/\"){ //remove trailing '/'\n              tagName = tagName.substr(0, tagName.length - 1);\n              jPath = jPath.substr(0, jPath.length - 1);\n              tagExp = tagName;\n            }else{\n              tagExp = tagExp.substr(0, tagExp.length - 1);\n            }\n            \n            if(this.options.transformTagName) {\n              tagName = this.options.transformTagName(tagName);\n            }\n\n            const childNode = new xmlNode(tagName);\n            if(tagName !== tagExp && attrExpPresent){\n              childNode[\":@\"] = this.buildAttributesMap(tagExp, jPath, tagName);\n            }\n            this.addChild(currentNode, childNode, jPath, startIndex);\n            jPath = jPath.substr(0, jPath.lastIndexOf(\".\"));\n          }\n    //opening tag\n          else{\n            const childNode = new xmlNode( tagName);\n            this.tagsNodeStack.push(currentNode);\n            \n            if(tagName !== tagExp && attrExpPresent){\n              childNode[\":@\"] = this.buildAttributesMap(tagExp, jPath, tagName);\n            }\n            this.addChild(currentNode, childNode, jPath, startIndex);\n            currentNode = childNode;\n          }\n          textData = \"\";\n          i = closeIndex;\n        }\n      }\n    }else{\n      textData += xmlData[i];\n    }\n  }\n  return xmlObj.child;\n}\n\nfunction addChild(currentNode, childNode, jPath, startIndex){\n  // unset startIndex if not requested\n  if (!this.options.captureMetaData) startIndex = undefined;\n  const result = this.options.updateTag(childNode.tagname, jPath, childNode[\":@\"])\n  if(result === false){\n  } else if(typeof result === \"string\"){\n    childNode.tagname = result\n    currentNode.addChild(childNode, startIndex);\n  }else{\n    currentNode.addChild(childNode, startIndex);\n  }\n}\n\nconst replaceEntitiesValue = function(val){\n\n  if(this.options.processEntities){\n    for(let entityName in this.docTypeEntities){\n      const entity = this.docTypeEntities[entityName];\n      val = val.replace( entity.regx, entity.val);\n    }\n    for(let entityName in this.lastEntities){\n      const entity = this.lastEntities[entityName];\n      val = val.replace( entity.regex, entity.val);\n    }\n    if(this.options.htmlEntities){\n      for(let entityName in this.htmlEntities){\n        const entity = this.htmlEntities[entityName];\n        val = val.replace( entity.regex, entity.val);\n      }\n    }\n    val = val.replace( this.ampEntity.regex, this.ampEntity.val);\n  }\n  return val;\n}\nfunction saveTextToParentTag(textData, currentNode, jPath, isLeafNode) {\n  if (textData) { //store previously collected data as textNode\n    if(isLeafNode === undefined) isLeafNode = currentNode.child.length === 0\n    \n    textData = this.parseTextData(textData,\n      currentNode.tagname,\n      jPath,\n      false,\n      currentNode[\":@\"] ? Object.keys(currentNode[\":@\"]).length !== 0 : false,\n      isLeafNode);\n\n    if (textData !== undefined && textData !== \"\")\n      currentNode.add(this.options.textNodeName, textData);\n    textData = \"\";\n  }\n  return textData;\n}\n\n//TODO: use jPath to simplify the logic\n/**\n * \n * @param {string[]} stopNodes \n * @param {string} jPath\n * @param {string} currentTagName \n */\nfunction isItStopNode(stopNodes, jPath, currentTagName){\n  const allNodesExp = \"*.\" + currentTagName;\n  for (const stopNodePath in stopNodes) {\n    const stopNodeExp = stopNodes[stopNodePath];\n    if( allNodesExp === stopNodeExp || jPath === stopNodeExp  ) return true;\n  }\n  return false;\n}\n\n/**\n * Returns the tag Expression and where it is ending handling single-double quotes situation\n * @param {string} xmlData \n * @param {number} i starting index\n * @returns \n */\nfunction tagExpWithClosingIndex(xmlData, i, closingChar = \">\"){\n  let attrBoundary;\n  let tagExp = \"\";\n  for (let index = i; index < xmlData.length; index++) {\n    let ch = xmlData[index];\n    if (attrBoundary) {\n        if (ch === attrBoundary) attrBoundary = \"\";//reset\n    } else if (ch === '\"' || ch === \"'\") {\n        attrBoundary = ch;\n    } else if (ch === closingChar[0]) {\n      if(closingChar[1]){\n        if(xmlData[index + 1] === closingChar[1]){\n          return {\n            data: tagExp,\n            index: index\n          }\n        }\n      }else{\n        return {\n          data: tagExp,\n          index: index\n        }\n      }\n    } else if (ch === '\\t') {\n      ch = \" \"\n    }\n    tagExp += ch;\n  }\n}\n\nfunction findClosingIndex(xmlData, str, i, errMsg){\n  const closingIndex = xmlData.indexOf(str, i);\n  if(closingIndex === -1){\n    throw new Error(errMsg)\n  }else{\n    return closingIndex + str.length - 1;\n  }\n}\n\nfunction readTagExp(xmlData,i, removeNSPrefix, closingChar = \">\"){\n  const result = tagExpWithClosingIndex(xmlData, i+1, closingChar);\n  if(!result) return;\n  let tagExp = result.data;\n  const closeIndex = result.index;\n  const separatorIndex = tagExp.search(/\\s/);\n  let tagName = tagExp;\n  let attrExpPresent = true;\n  if(separatorIndex !== -1){//separate tag name and attributes expression\n    tagName = tagExp.substring(0, separatorIndex);\n    tagExp = tagExp.substring(separatorIndex + 1).trimStart();\n  }\n\n  const rawTagName = tagName;\n  if(removeNSPrefix){\n    const colonIndex = tagName.indexOf(\":\");\n    if(colonIndex !== -1){\n      tagName = tagName.substr(colonIndex+1);\n      attrExpPresent = tagName !== result.data.substr(colonIndex + 1);\n    }\n  }\n\n  return {\n    tagName: tagName,\n    tagExp: tagExp,\n    closeIndex: closeIndex,\n    attrExpPresent: attrExpPresent,\n    rawTagName: rawTagName,\n  }\n}\n/**\n * find paired tag for a stop node\n * @param {string} xmlData \n * @param {string} tagName \n * @param {number} i \n */\nfunction readStopNodeData(xmlData, tagName, i){\n  const startIndex = i;\n  // Starting at 1 since we already have an open tag\n  let openTagCount = 1;\n\n  for (; i < xmlData.length; i++) {\n    if( xmlData[i] === \"<\"){ \n      if (xmlData[i+1] === \"/\") {//close tag\n          const closeIndex = findClosingIndex(xmlData, \">\", i, `${tagName} is not closed`);\n          let closeTagName = xmlData.substring(i+2,closeIndex).trim();\n          if(closeTagName === tagName){\n            openTagCount--;\n            if (openTagCount === 0) {\n              return {\n                tagContent: xmlData.substring(startIndex, i),\n                i : closeIndex\n              }\n            }\n          }\n          i=closeIndex;\n        } else if(xmlData[i+1] === '?') { \n          const closeIndex = findClosingIndex(xmlData, \"?>\", i+1, \"StopNode is not closed.\")\n          i=closeIndex;\n        } else if(xmlData.substr(i + 1, 3) === '!--') { \n          const closeIndex = findClosingIndex(xmlData, \"-->\", i+3, \"StopNode is not closed.\")\n          i=closeIndex;\n        } else if(xmlData.substr(i + 1, 2) === '![') { \n          const closeIndex = findClosingIndex(xmlData, \"]]>\", i, \"StopNode is not closed.\") - 2;\n          i=closeIndex;\n        } else {\n          const tagData = readTagExp(xmlData, i, '>')\n\n          if (tagData) {\n            const openTagName = tagData && tagData.tagName;\n            if (openTagName === tagName && tagData.tagExp[tagData.tagExp.length-1] !== \"/\") {\n              openTagCount++;\n            }\n            i=tagData.closeIndex;\n          }\n        }\n      }\n  }//end for loop\n}\n\nfunction parseValue(val, shouldParse, options) {\n  if (shouldParse && typeof val === 'string') {\n    //console.log(options)\n    const newval = val.trim();\n    if(newval === 'true' ) return true;\n    else if(newval === 'false' ) return false;\n    else return toNumber(val, options);\n  } else {\n    if (isExist(val)) {\n      return val;\n    } else {\n      return '';\n    }\n  }\n}\n", "export default function getIgnoreAttributesFn(ignoreAttributes) {\n    if (typeof ignoreAttributes === 'function') {\n        return ignoreAttributes\n    }\n    if (Array.isArray(ignoreAttributes)) {\n        return (attrName) => {\n            for (const pattern of ignoreAttributes) {\n                if (typeof pattern === 'string' && attrName === pattern) {\n                    return true\n                }\n                if (pattern instanceof RegExp && pattern.test(attrName)) {\n                    return true\n                }\n            }\n        }\n    }\n    return () => false\n}", "'use strict';\n\nimport XmlNode from './xmlNode.js';\n\nconst METADATA_SYMBOL = XmlNode.getMetaDataSymbol();\n\n/**\n * \n * @param {array} node \n * @param {any} options \n * @returns \n */\nexport default function prettify(node, options){\n  return compress( node, options);\n}\n\n/**\n * \n * @param {array} arr \n * @param {object} options \n * @param {string} jPath \n * @returns object\n */\nfunction compress(arr, options, jPath){\n  let text;\n  const compressedObj = {};\n  for (let i = 0; i < arr.length; i++) {\n    const tagObj = arr[i];\n    const property = propName(tagObj);\n    let newJpath = \"\";\n    if(jPath === undefined) newJpath = property;\n    else newJpath = jPath + \".\" + property;\n\n    if(property === options.textNodeName){\n      if(text === undefined) text = tagObj[property];\n      else text += \"\" + tagObj[property];\n    }else if(property === undefined){\n      continue;\n    }else if(tagObj[property]){\n      \n      let val = compress(tagObj[property], options, newJpath);\n      const isLeaf = isLeafTag(val, options);\n      if (tagObj[METADATA_SYMBOL] !== undefined) {\n        val[METADATA_SYMBOL] = tagObj[METADATA_SYMBOL]; // copy over metadata\n      }\n\n      if(tagObj[\":@\"]){\n        assignAttributes( val, tagObj[\":@\"], newJpath, options);\n      }else if(Object.keys(val).length === 1 && val[options.textNodeName] !== undefined && !options.alwaysCreateTextNode){\n        val = val[options.textNodeName];\n      }else if(Object.keys(val).length === 0){\n        if(options.alwaysCreateTextNode) val[options.textNodeName] = \"\";\n        else val = \"\";\n      }\n\n      if(compressedObj[property] !== undefined && compressedObj.hasOwnProperty(property)) {\n        if(!Array.isArray(compressedObj[property])) {\n            compressedObj[property] = [ compressedObj[property] ];\n        }\n        compressedObj[property].push(val);\n      }else{\n        //TODO: if a node is not an array, then check if it should be an array\n        //also determine if it is a leaf node\n        if (options.isArray(property, newJpath, isLeaf )) {\n          compressedObj[property] = [val];\n        }else{\n          compressedObj[property] = val;\n        }\n      }\n    }\n    \n  }\n  // if(text && text.length > 0) compressedObj[options.textNodeName] = text;\n  if(typeof text === \"string\"){\n    if(text.length > 0) compressedObj[options.textNodeName] = text;\n  }else if(text !== undefined) compressedObj[options.textNodeName] = text;\n  return compressedObj;\n}\n\nfunction propName(obj){\n  const keys = Object.keys(obj);\n  for (let i = 0; i < keys.length; i++) {\n    const key = keys[i];\n    if(key !== \":@\") return key;\n  }\n}\n\nfunction assignAttributes(obj, attrMap, jpath, options){\n  if (attrMap) {\n    const keys = Object.keys(attrMap);\n    const len = keys.length; //don't make it inline\n    for (let i = 0; i < len; i++) {\n      const atrrName = keys[i];\n      if (options.isArray(atrrName, jpath + \".\" + atrrName, true, true)) {\n        obj[atrrName] = [ attrMap[atrrName] ];\n      } else {\n        obj[atrrName] = attrMap[atrrName];\n      }\n    }\n  }\n}\n\nfunction isLeafTag(obj, options){\n  const { textNodeName } = options;\n  const propCount = Object.keys(obj).length;\n  \n  if (propCount === 0) {\n    return true;\n  }\n\n  if (\n    propCount === 1 &&\n    (obj[textNodeName] || typeof obj[textNodeName] === \"boolean\" || obj[textNodeName] === 0)\n  ) {\n    return true;\n  }\n\n  return false;\n}\n", "'use strict';\n\nimport {getAllMatches, isName} from './util.js';\n\nconst defaultOptions = {\n  allowBooleanAttributes: false, //A tag can have attributes without any value\n  unpairedTags: []\n};\n\n//const tagsPattern = new RegExp(\"<\\\\/?([\\\\w:\\\\-_\\.]+)\\\\s*\\/?>\",\"g\");\nexport function validate(xmlData, options) {\n  options = Object.assign({}, defaultOptions, options);\n\n  //xmlData = xmlData.replace(/(\\r\\n|\\n|\\r)/gm,\"\");//make it single line\n  //xmlData = xmlData.replace(/(^\\s*<\\?xml.*?\\?>)/g,\"\");//Remove XML starting tag\n  //xmlData = xmlData.replace(/(<!DOCTYPE[\\s\\w\\\"\\.\\/\\-\\:]+(\\[.*\\])*\\s*>)/g,\"\");//Remove DOCTYPE\n  const tags = [];\n  let tagFound = false;\n\n  //indicates that the root tag has been closed (aka. depth 0 has been reached)\n  let reachedRoot = false;\n\n  if (xmlData[0] === '\\ufeff') {\n    // check for byte order mark (BOM)\n    xmlData = xmlData.substr(1);\n  }\n  \n  for (let i = 0; i < xmlData.length; i++) {\n\n    if (xmlData[i] === '<' && xmlData[i+1] === '?') {\n      i+=2;\n      i = readPI(xmlData,i);\n      if (i.err) return i;\n    }else if (xmlData[i] === '<') {\n      //starting of tag\n      //read until you reach to '>' avoiding any '>' in attribute value\n      let tagStartPos = i;\n      i++;\n      \n      if (xmlData[i] === '!') {\n        i = readCommentAndCDATA(xmlData, i);\n        continue;\n      } else {\n        let closingTag = false;\n        if (xmlData[i] === '/') {\n          //closing tag\n          closingTag = true;\n          i++;\n        }\n        //read tagname\n        let tagName = '';\n        for (; i < xmlData.length &&\n          xmlData[i] !== '>' &&\n          xmlData[i] !== ' ' &&\n          xmlData[i] !== '\\t' &&\n          xmlData[i] !== '\\n' &&\n          xmlData[i] !== '\\r'; i++\n        ) {\n          tagName += xmlData[i];\n        }\n        tagName = tagName.trim();\n        //console.log(tagName);\n\n        if (tagName[tagName.length - 1] === '/') {\n          //self closing tag without attributes\n          tagName = tagName.substring(0, tagName.length - 1);\n          //continue;\n          i--;\n        }\n        if (!validateTagName(tagName)) {\n          let msg;\n          if (tagName.trim().length === 0) {\n            msg = \"Invalid space after '<'.\";\n          } else {\n            msg = \"Tag '\"+tagName+\"' is an invalid name.\";\n          }\n          return getErrorObject('InvalidTag', msg, getLineNumberForPosition(xmlData, i));\n        }\n\n        const result = readAttributeStr(xmlData, i);\n        if (result === false) {\n          return getErrorObject('InvalidAttr', \"Attributes for '\"+tagName+\"' have open quote.\", getLineNumberForPosition(xmlData, i));\n        }\n        let attrStr = result.value;\n        i = result.index;\n\n        if (attrStr[attrStr.length - 1] === '/') {\n          //self closing tag\n          const attrStrStart = i - attrStr.length;\n          attrStr = attrStr.substring(0, attrStr.length - 1);\n          const isValid = validateAttributeString(attrStr, options);\n          if (isValid === true) {\n            tagFound = true;\n            //continue; //text may presents after self closing tag\n          } else {\n            //the result from the nested function returns the position of the error within the attribute\n            //in order to get the 'true' error line, we need to calculate the position where the attribute begins (i - attrStr.length) and then add the position within the attribute\n            //this gives us the absolute index in the entire xml, which we can use to find the line at last\n            return getErrorObject(isValid.err.code, isValid.err.msg, getLineNumberForPosition(xmlData, attrStrStart + isValid.err.line));\n          }\n        } else if (closingTag) {\n          if (!result.tagClosed) {\n            return getErrorObject('InvalidTag', \"Closing tag '\"+tagName+\"' doesn't have proper closing.\", getLineNumberForPosition(xmlData, i));\n          } else if (attrStr.trim().length > 0) {\n            return getErrorObject('InvalidTag', \"Closing tag '\"+tagName+\"' can't have attributes or invalid starting.\", getLineNumberForPosition(xmlData, tagStartPos));\n          } else if (tags.length === 0) {\n            return getErrorObject('InvalidTag', \"Closing tag '\"+tagName+\"' has not been opened.\", getLineNumberForPosition(xmlData, tagStartPos));\n          } else {\n            const otg = tags.pop();\n            if (tagName !== otg.tagName) {\n              let openPos = getLineNumberForPosition(xmlData, otg.tagStartPos);\n              return getErrorObject('InvalidTag',\n                \"Expected closing tag '\"+otg.tagName+\"' (opened in line \"+openPos.line+\", col \"+openPos.col+\") instead of closing tag '\"+tagName+\"'.\",\n                getLineNumberForPosition(xmlData, tagStartPos));\n            }\n\n            //when there are no more tags, we reached the root level.\n            if (tags.length == 0) {\n              reachedRoot = true;\n            }\n          }\n        } else {\n          const isValid = validateAttributeString(attrStr, options);\n          if (isValid !== true) {\n            //the result from the nested function returns the position of the error within the attribute\n            //in order to get the 'true' error line, we need to calculate the position where the attribute begins (i - attrStr.length) and then add the position within the attribute\n            //this gives us the absolute index in the entire xml, which we can use to find the line at last\n            return getErrorObject(isValid.err.code, isValid.err.msg, getLineNumberForPosition(xmlData, i - attrStr.length + isValid.err.line));\n          }\n\n          //if the root level has been reached before ...\n          if (reachedRoot === true) {\n            return getErrorObject('InvalidXml', 'Multiple possible root nodes found.', getLineNumberForPosition(xmlData, i));\n          } else if(options.unpairedTags.indexOf(tagName) !== -1){\n            //don't push into stack\n          } else {\n            tags.push({tagName, tagStartPos});\n          }\n          tagFound = true;\n        }\n\n        //skip tag text value\n        //It may include comments and CDATA value\n        for (i++; i < xmlData.length; i++) {\n          if (xmlData[i] === '<') {\n            if (xmlData[i + 1] === '!') {\n              //comment or CADATA\n              i++;\n              i = readCommentAndCDATA(xmlData, i);\n              continue;\n            } else if (xmlData[i+1] === '?') {\n              i = readPI(xmlData, ++i);\n              if (i.err) return i;\n            } else{\n              break;\n            }\n          } else if (xmlData[i] === '&') {\n            const afterAmp = validateAmpersand(xmlData, i);\n            if (afterAmp == -1)\n              return getErrorObject('InvalidChar', \"char '&' is not expected.\", getLineNumberForPosition(xmlData, i));\n            i = afterAmp;\n          }else{\n            if (reachedRoot === true && !isWhiteSpace(xmlData[i])) {\n              return getErrorObject('InvalidXml', \"Extra text at the end\", getLineNumberForPosition(xmlData, i));\n            }\n          }\n        } //end of reading tag text value\n        if (xmlData[i] === '<') {\n          i--;\n        }\n      }\n    } else {\n      if ( isWhiteSpace(xmlData[i])) {\n        continue;\n      }\n      return getErrorObject('InvalidChar', \"char '\"+xmlData[i]+\"' is not expected.\", getLineNumberForPosition(xmlData, i));\n    }\n  }\n\n  if (!tagFound) {\n    return getErrorObject('InvalidXml', 'Start tag expected.', 1);\n  }else if (tags.length == 1) {\n      return getErrorObject('InvalidTag', \"Unclosed tag '\"+tags[0].tagName+\"'.\", getLineNumberForPosition(xmlData, tags[0].tagStartPos));\n  }else if (tags.length > 0) {\n      return getErrorObject('InvalidXml', \"Invalid '\"+\n          JSON.stringify(tags.map(t => t.tagName), null, 4).replace(/\\r?\\n/g, '')+\n          \"' found.\", {line: 1, col: 1});\n  }\n\n  return true;\n};\n\nfunction isWhiteSpace(char){\n  return char === ' ' || char === '\\t' || char === '\\n'  || char === '\\r';\n}\n/**\n * Read Processing insstructions and skip\n * @param {*} xmlData\n * @param {*} i\n */\nfunction readPI(xmlData, i) {\n  const start = i;\n  for (; i < xmlData.length; i++) {\n    if (xmlData[i] == '?' || xmlData[i] == ' ') {\n      //tagname\n      const tagname = xmlData.substr(start, i - start);\n      if (i > 5 && tagname === 'xml') {\n        return getErrorObject('InvalidXml', 'XML declaration allowed only at the start of the document.', getLineNumberForPosition(xmlData, i));\n      } else if (xmlData[i] == '?' && xmlData[i + 1] == '>') {\n        //check if valid attribut string\n        i++;\n        break;\n      } else {\n        continue;\n      }\n    }\n  }\n  return i;\n}\n\nfunction readCommentAndCDATA(xmlData, i) {\n  if (xmlData.length > i + 5 && xmlData[i + 1] === '-' && xmlData[i + 2] === '-') {\n    //comment\n    for (i += 3; i < xmlData.length; i++) {\n      if (xmlData[i] === '-' && xmlData[i + 1] === '-' && xmlData[i + 2] === '>') {\n        i += 2;\n        break;\n      }\n    }\n  } else if (\n    xmlData.length > i + 8 &&\n    xmlData[i + 1] === 'D' &&\n    xmlData[i + 2] === 'O' &&\n    xmlData[i + 3] === 'C' &&\n    xmlData[i + 4] === 'T' &&\n    xmlData[i + 5] === 'Y' &&\n    xmlData[i + 6] === 'P' &&\n    xmlData[i + 7] === 'E'\n  ) {\n    let angleBracketsCount = 1;\n    for (i += 8; i < xmlData.length; i++) {\n      if (xmlData[i] === '<') {\n        angleBracketsCount++;\n      } else if (xmlData[i] === '>') {\n        angleBracketsCount--;\n        if (angleBracketsCount === 0) {\n          break;\n        }\n      }\n    }\n  } else if (\n    xmlData.length > i + 9 &&\n    xmlData[i + 1] === '[' &&\n    xmlData[i + 2] === 'C' &&\n    xmlData[i + 3] === 'D' &&\n    xmlData[i + 4] === 'A' &&\n    xmlData[i + 5] === 'T' &&\n    xmlData[i + 6] === 'A' &&\n    xmlData[i + 7] === '['\n  ) {\n    for (i += 8; i < xmlData.length; i++) {\n      if (xmlData[i] === ']' && xmlData[i + 1] === ']' && xmlData[i + 2] === '>') {\n        i += 2;\n        break;\n      }\n    }\n  }\n\n  return i;\n}\n\nconst doubleQuote = '\"';\nconst singleQuote = \"'\";\n\n/**\n * Keep reading xmlData until '<' is found outside the attribute value.\n * @param {string} xmlData\n * @param {number} i\n */\nfunction readAttributeStr(xmlData, i) {\n  let attrStr = '';\n  let startChar = '';\n  let tagClosed = false;\n  for (; i < xmlData.length; i++) {\n    if (xmlData[i] === doubleQuote || xmlData[i] === singleQuote) {\n      if (startChar === '') {\n        startChar = xmlData[i];\n      } else if (startChar !== xmlData[i]) {\n        //if vaue is enclosed with double quote then single quotes are allowed inside the value and vice versa\n      } else {\n        startChar = '';\n      }\n    } else if (xmlData[i] === '>') {\n      if (startChar === '') {\n        tagClosed = true;\n        break;\n      }\n    }\n    attrStr += xmlData[i];\n  }\n  if (startChar !== '') {\n    return false;\n  }\n\n  return {\n    value: attrStr,\n    index: i,\n    tagClosed: tagClosed\n  };\n}\n\n/**\n * Select all the attributes whether valid or invalid.\n */\nconst validAttrStrRegxp = new RegExp('(\\\\s*)([^\\\\s=]+)(\\\\s*=)?(\\\\s*([\\'\"])(([\\\\s\\\\S])*?)\\\\5)?', 'g');\n\n//attr, =\"sd\", a=\"amit's\", a=\"sd\"b=\"saf\", ab  cd=\"\"\n\nfunction validateAttributeString(attrStr, options) {\n  //console.log(\"start:\"+attrStr+\":end\");\n\n  //if(attrStr.trim().length === 0) return true; //empty string\n\n  const matches = getAllMatches(attrStr, validAttrStrRegxp);\n  const attrNames = {};\n\n  for (let i = 0; i < matches.length; i++) {\n    if (matches[i][1].length === 0) {\n      //nospace before attribute name: a=\"sd\"b=\"saf\"\n      return getErrorObject('InvalidAttr', \"Attribute '\"+matches[i][2]+\"' has no space in starting.\", getPositionFromMatch(matches[i]))\n    } else if (matches[i][3] !== undefined && matches[i][4] === undefined) {\n      return getErrorObject('InvalidAttr', \"Attribute '\"+matches[i][2]+\"' is without value.\", getPositionFromMatch(matches[i]));\n    } else if (matches[i][3] === undefined && !options.allowBooleanAttributes) {\n      //independent attribute: ab\n      return getErrorObject('InvalidAttr', \"boolean attribute '\"+matches[i][2]+\"' is not allowed.\", getPositionFromMatch(matches[i]));\n    }\n    /* else if(matches[i][6] === undefined){//attribute without value: ab=\n                    return { err: { code:\"InvalidAttr\",msg:\"attribute \" + matches[i][2] + \" has no value assigned.\"}};\n                } */\n    const attrName = matches[i][2];\n    if (!validateAttrName(attrName)) {\n      return getErrorObject('InvalidAttr', \"Attribute '\"+attrName+\"' is an invalid name.\", getPositionFromMatch(matches[i]));\n    }\n    if (!attrNames.hasOwnProperty(attrName)) {\n      //check for duplicate attribute.\n      attrNames[attrName] = 1;\n    } else {\n      return getErrorObject('InvalidAttr', \"Attribute '\"+attrName+\"' is repeated.\", getPositionFromMatch(matches[i]));\n    }\n  }\n\n  return true;\n}\n\nfunction validateNumberAmpersand(xmlData, i) {\n  let re = /\\d/;\n  if (xmlData[i] === 'x') {\n    i++;\n    re = /[\\da-fA-F]/;\n  }\n  for (; i < xmlData.length; i++) {\n    if (xmlData[i] === ';')\n      return i;\n    if (!xmlData[i].match(re))\n      break;\n  }\n  return -1;\n}\n\nfunction validateAmpersand(xmlData, i) {\n  // https://www.w3.org/TR/xml/#dt-charref\n  i++;\n  if (xmlData[i] === ';')\n    return -1;\n  if (xmlData[i] === '#') {\n    i++;\n    return validateNumberAmpersand(xmlData, i);\n  }\n  let count = 0;\n  for (; i < xmlData.length; i++, count++) {\n    if (xmlData[i].match(/\\w/) && count < 20)\n      continue;\n    if (xmlData[i] === ';')\n      break;\n    return -1;\n  }\n  return i;\n}\n\nfunction getErrorObject(code, message, lineNumber) {\n  return {\n    err: {\n      code: code,\n      msg: message,\n      line: lineNumber.line || lineNumber,\n      col: lineNumber.col,\n    },\n  };\n}\n\nfunction validateAttrName(attrName) {\n  return isName(attrName);\n}\n\n// const startsWithXML = /^xml/i;\n\nfunction validateTagName(tagname) {\n  return isName(tagname) /* && !tagname.match(startsWithXML) */;\n}\n\n//this function returns the line number for the character at the given index\nfunction getLineNumberForPosition(xmlData, index) {\n  const lines = xmlData.substring(0, index).split(/\\r?\\n/);\n  return {\n    line: lines.length,\n\n    // column number is last line's length + 1, because column numbering starts at 1:\n    col: lines[lines.length - 1].length + 1\n  };\n}\n\n//this function returns the position of the first character of match within attrStr\nfunction getPositionFromMatch(match) {\n  return match.startIndex + match[1].length;\n}\n", "import { buildOptions} from './OptionsBuilder.js';\nimport OrderedObjParser from './OrderedObjParser.js';\nimport prettify from './node2json.js';\nimport {validate} from \"../validator.js\";\nimport XmlNode from './xmlNode.js';\n\nexport default class XMLParser{\n    \n    constructor(options){\n        this.externalEntities = {};\n        this.options = buildOptions(options);\n        \n    }\n    /**\n     * Parse XML dats to JS object \n     * @param {string|Buffer} xmlData \n     * @param {boolean|Object} validationOption \n     */\n    parse(xmlData,validationOption){\n        if(typeof xmlData === \"string\"){\n        }else if( xmlData.toString){\n            xmlData = xmlData.toString();\n        }else{\n            throw new Error(\"XML data is accepted in String or Bytes[] form.\")\n        }\n        if( validationOption){\n            if(validationOption === true) validationOption = {}; //validate with default options\n            \n            const result = validate(xmlData, validationOption);\n            if (result !== true) {\n              throw Error( `${result.err.msg}:${result.err.line}:${result.err.col}` )\n            }\n          }\n        const orderedObjParser = new OrderedObjParser(this.options);\n        orderedObjParser.addExternalEntities(this.externalEntities);\n        const orderedResult = orderedObjParser.parseXml(xmlData);\n        if(this.options.preserveOrder || orderedResult === undefined) return orderedResult;\n        else return prettify(orderedResult, this.options);\n    }\n\n    /**\n     * Add Entity which is not by default supported by this library\n     * @param {string} key \n     * @param {string} value \n     */\n    addEntity(key, value){\n        if(value.indexOf(\"&\") !== -1){\n            throw new Error(\"Entity value can't have '&'\")\n        }else if(key.indexOf(\"&\") !== -1 || key.indexOf(\";\") !== -1){\n            throw new Error(\"An entity must be set without '&' and ';'. Eg. use '#xD' for '&#xD;'\")\n        }else if(value === \"&\"){\n            throw new Error(\"An entity with value '&' is not permitted\");\n        }else{\n            this.externalEntities[key] = value;\n        }\n    }\n\n    /**\n     * Returns a Symbol that can be used to access the metadata\n     * property on a node.\n     * \n     * If Symbol is not available in the environment, an ordinary property is used\n     * and the name of the property is here returned.\n     * \n     * The XMLMetaData property is only present when `captureMetaData`\n     * is true in the options.\n     */\n    static getMetaDataSymbol() {\n        return XmlNode.getMetaDataSymbol();\n    }\n}\n"], "names": ["root", "factory", "exports", "module", "define", "amd", "this", "__webpack_require__", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "Symbol", "toStringTag", "value", "defaultOptions", "preserveOrder", "attributeNamePrefix", "attributesGroupName", "textNodeName", "ignoreAttributes", "removeNSPrefix", "allowBooleanAttributes", "parseTagValue", "parseAttributeValue", "trimValues", "cdataPropName", "numberParseOptions", "hex", "leadingZeros", "eNotation", "tagValueProcessor", "tagName", "val", "attributeValueProcessor", "attrName", "stopNodes", "alwaysCreateTextNode", "isArray", "commentPropName", "unpairedTags", "processEntities", "htmlEntities", "ignoreDeclaration", "ignorePiTags", "transformTagName", "transformAttributeName", "updateTag", "jPath", "attrs", "captureMetaData", "nameStartChar", "regexName", "RegExp", "getAllMatches", "string", "regex", "matches", "match", "exec", "allmatches", "startIndex", "lastIndex", "length", "len", "index", "push", "METADATA_SYMBOL", "isName", "XmlNode", "tagname", "child", "_proto", "add", "_this$child$push", "<PERSON><PERSON><PERSON><PERSON>", "node", "_this$child$push2", "_this$child$push3", "keys", "undefined", "getMetaDataSymbol", "readDocType", "xmlData", "i", "entities", "Error", "angleBracketsCount", "hasBody", "comment", "hasSeq", "entityName", "_readEntityExp", "readEntityExp", "indexOf", "regx", "readElementExp", "readNotationExp", "skipWhitespace", "data", "test", "validateEntityName", "substring", "toUpperCase", "_readIdentifierVal", "readIdentifierVal", "notationName", "identifierType", "publicIdentifier", "systemIdentifier", "_readIdentifierVal2", "_readIdentifierVal3", "_readIdentifierVal4", "type", "identifierVal", "startChar", "elementName", "contentModel", "trim", "seq", "j", "name", "hexRegex", "numRegex", "consider", "decimalPoint", "eNotationRegx", "Ordered<PERSON>bj<PERSON><PERSON><PERSON>", "options", "currentNode", "tagsNodeStack", "docTypeEntities", "lastEntities", "ampEntity", "_", "str", "String", "fromCodePoint", "Number", "parseInt", "addExternalEntities", "parseXml", "parseTextData", "resolveNameSpace", "buildAttributesMap", "isItStopNode", "replaceEntitiesValue", "readStopNodeData", "saveTextToParentTag", "ignoreAttributesFn", "Array", "_step", "_iterator", "_createForOfIteratorHelperLoose", "done", "pattern", "externalEntities", "ent<PERSON><PERSON>s", "ent", "dontTrim", "hasAttributes", "isLeafNode", "escapeEntities", "newval", "parseValue", "tags", "split", "prefix", "char<PERSON>t", "attrsRegx", "attrStr", "oldVal", "aName", "newVal", "attrCollection", "replace", "xmlObj", "xmlNode", "textData", "closeIndex", "findClosingIndex", "colonIndex", "substr", "lastTagName", "lastIndexOf", "propIndex", "pop", "tagData", "readTagExp", "childNode", "tagExp", "attrExpPresent", "endIndex", "_ref", "result", "_ref2", "rawTagName", "lastTag", "tagContent", "entity", "currentTagName", "allNodesExp", "stopNodePath", "stopNodeExp", "errMsg", "closingIndex", "closingChar", "attrBoundary", "ch", "tagExpWithClosingIndex", "separatorIndex", "search", "trimStart", "openTagCount", "<PERSON><PERSON><PERSON><PERSON>", "assign", "trimmedStr", "skipLike", "numStr", "window", "parse_int", "notation", "sign", "eChar", "eAdjacentToLeadingZeros", "startsWith", "resolveEnotation", "numTrimmedByZeros", "decimalAdjacentToLeadingZeros", "num", "parsedStr", "n", "toNumber", "prettify", "compress", "arr", "text", "compressedObj", "newJpath", "tagObj", "property", "propName", "<PERSON><PERSON><PERSON><PERSON>", "isLeafTag", "assignAttributes", "attrMap", "jpath", "atrrName", "propCount", "isWhiteSpace", "char", "readPI", "start", "getErrorObject", "getLineNumberForPosition", "readCommentAndCDATA", "readAttributeStr", "tagClosed", "validAttrStrRegxp", "validateAttributeString", "attrNames", "getPositionFromMatch", "validateAttrName", "validateAmpersand", "re", "validateNumberAmpersand", "count", "code", "message", "lineNumber", "err", "msg", "line", "col", "lines", "XMLParser", "buildOptions", "parse", "validationOption", "toString", "tagFound", "reachedRoot", "tagStartPos", "closingTag", "attrStrStart", "<PERSON><PERSON><PERSON><PERSON>", "otg", "openPos", "afterAmp", "JSON", "stringify", "map", "t", "validate", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "orderedResult", "addEntity"], "sourceRoot": ""}