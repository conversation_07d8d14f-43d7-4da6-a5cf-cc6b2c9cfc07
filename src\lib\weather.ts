import axios from 'axios'

export interface WeatherData {
  temperature: number
  humidity: number
  windSpeed: number
  windDirection: number
  precipitation: number
  snowfall: number
  visibility: number
  pressure: number
  cloudCover: number
  conditions: string
  forecast: ForecastDay[]
}

export interface ForecastDay {
  date: string
  dayOfWeek: string
  tempHigh: number
  tempLow: number
  precipitation: number
  snowfall: number
  windSpeed: number
  conditions: string
  precipitationChance: number
}

export interface LocationData {
  zipCode: string
  city: string
  state: string
  country: string
  latitude: number
  longitude: number
}

class WeatherService {
  private apiKey: string
  private baseUrl: string

  constructor() {
    this.apiKey = process.env.WEATHER_API_KEY || ''
    this.baseUrl = process.env.NEXT_PUBLIC_WEATHER_API_URL || 'https://api.openweathermap.org/data/2.5'
  }

  async getLocationFromZipCode(zipCode: string): Promise<LocationData> {
    try {
      // Handle Canadian postal codes
      const isCanadian = /^[A-Za-z]\d[A-Za-z] \d[A-Za-z]\d$/.test(zipCode)
      const country = isCanadian ? 'CA' : 'US'
      
      const response = await axios.get(`${this.baseUrl}/weather`, {
        params: {
          zip: `${zipCode},${country}`,
          appid: this.apiKey,
          units: 'imperial'
        }
      })

      const data = response.data
      return {
        zipCode,
        city: data.name,
        state: data.sys.state || '',
        country: data.sys.country,
        latitude: data.coord.lat,
        longitude: data.coord.lon
      }
    } catch (error) {
      console.error('Error fetching location data:', error)
      throw new Error('Invalid zip code or location not found')
    }
  }

  async getCurrentWeather(zipCode: string): Promise<WeatherData> {
    try {
      const location = await this.getLocationFromZipCode(zipCode)
      
      // Get current weather
      const currentResponse = await axios.get(`${this.baseUrl}/weather`, {
        params: {
          lat: location.latitude,
          lon: location.longitude,
          appid: this.apiKey,
          units: 'imperial'
        }
      })

      // Get 5-day forecast
      const forecastResponse = await axios.get(`${this.baseUrl}/forecast`, {
        params: {
          lat: location.latitude,
          lon: location.longitude,
          appid: this.apiKey,
          units: 'imperial'
        }
      })

      const current = currentResponse.data
      const forecast = forecastResponse.data

      // Process forecast data for next 2 days
      const forecastDays = this.processForecastData(forecast.list)

      return {
        temperature: Math.round(current.main.temp),
        humidity: current.main.humidity,
        windSpeed: Math.round(current.wind.speed),
        windDirection: current.wind.deg || 0,
        precipitation: current.rain?.['1h'] || current.snow?.['1h'] || 0,
        snowfall: current.snow?.['1h'] || 0,
        visibility: current.visibility ? current.visibility / 1000 : 10, // Convert to miles
        pressure: current.main.pressure,
        cloudCover: current.clouds.all,
        conditions: current.weather[0].description,
        forecast: forecastDays
      }
    } catch (error) {
      console.error('Error fetching weather data:', error)
      throw new Error('Unable to fetch weather data')
    }
  }

  private processForecastData(forecastList: any[]): ForecastDay[] {
    const days: { [key: string]: any[] } = {}
    
    // Group forecast data by date
    forecastList.forEach(item => {
      const date = new Date(item.dt * 1000).toDateString()
      if (!days[date]) {
        days[date] = []
      }
      days[date].push(item)
    })

    // Process only next 2 days
    const sortedDates = Object.keys(days).sort().slice(0, 2)
    
    return sortedDates.map(dateStr => {
      const dayData = days[dateStr]
      const date = new Date(dateStr)
      
      // Calculate daily aggregates
      const temps = dayData.map(d => d.main.temp)
      const precipitations = dayData.map(d => (d.rain?.['3h'] || 0) + (d.snow?.['3h'] || 0))
      const snowfalls = dayData.map(d => d.snow?.['3h'] || 0)
      const winds = dayData.map(d => d.wind.speed)
      const precipChances = dayData.map(d => d.pop * 100)

      return {
        date: date.toISOString().split('T')[0],
        dayOfWeek: date.toLocaleDateString('en-US', { weekday: 'long' }),
        tempHigh: Math.round(Math.max(...temps)),
        tempLow: Math.round(Math.min(...temps)),
        precipitation: Math.round(Math.max(...precipitations) * 10) / 10,
        snowfall: Math.round(Math.max(...snowfalls) * 10) / 10,
        windSpeed: Math.round(Math.max(...winds)),
        conditions: dayData[0].weather[0].description,
        precipitationChance: Math.round(Math.max(...precipChances))
      }
    })
  }

  // Fallback method using mock data for development
  async getMockWeatherData(zipCode: string): Promise<WeatherData> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000))

    const mockForecast: ForecastDay[] = [
      {
        date: new Date(Date.now() + 86400000).toISOString().split('T')[0],
        dayOfWeek: 'Tomorrow',
        tempHigh: 28,
        tempLow: 18,
        precipitation: 0.8,
        snowfall: 3.2,
        windSpeed: 15,
        conditions: 'Heavy snow',
        precipitationChance: 85
      },
      {
        date: new Date(Date.now() + 172800000).toISOString().split('T')[0],
        dayOfWeek: new Date(Date.now() + 172800000).toLocaleDateString('en-US', { weekday: 'long' }),
        tempHigh: 32,
        tempLow: 22,
        precipitation: 0.3,
        snowfall: 1.1,
        windSpeed: 8,
        conditions: 'Light snow',
        precipitationChance: 60
      }
    ]

    return {
      temperature: 25,
      humidity: 78,
      windSpeed: 12,
      windDirection: 270,
      precipitation: 0.5,
      snowfall: 2.1,
      visibility: 2.5,
      pressure: 1013,
      cloudCover: 90,
      conditions: 'Snow',
      forecast: mockForecast
    }
  }
}

export const weatherService = new WeatherService()
