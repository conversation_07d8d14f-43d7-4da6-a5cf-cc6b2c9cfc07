'use client'

import { motion, AnimatePresence } from 'framer-motion'
import { ReactNode } from 'react'

interface PageTransitionProps {
  children: ReactNode
  pageKey: string
}

// Slide transition (like original site)
export function SlideTransition({ children, pageKey }: PageTransitionProps) {
  return (
    <AnimatePresence mode="wait">
      <motion.div
        key={pageKey}
        initial={{ x: 100, opacity: 0, width: '64px' }}
        animate={{ x: 0, opacity: 1, width: '100%' }}
        exit={{ x: -100, opacity: 0, width: '64px' }}
        transition={{
          type: 'tween',
          ease: 'anticipate',
          duration: 0.6
        }}
        className="overflow-hidden"
      >
        {children}
      </motion.div>
    </AnimatePresence>
  )
}

// Fade transition
export function FadeTransition({ children, pageKey }: PageTransitionProps) {
  return (
    <AnimatePresence mode="wait">
      <motion.div
        key={pageKey}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        transition={{ duration: 0.4 }}
      >
        {children}
      </motion.div>
    </AnimatePresence>
  )
}

// Scale transition
export function ScaleTransition({ children, pageKey }: PageTransitionProps) {
  return (
    <AnimatePresence mode="wait">
      <motion.div
        key={pageKey}
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 1.1, opacity: 0 }}
        transition={{ duration: 0.3 }}
      >
        {children}
      </motion.div>
    </AnimatePresence>
  )
}

// Staggered children animation
export function StaggerContainer({ 
  children, 
  className = '',
  delay = 0.1 
}: { 
  children: ReactNode
  className?: string
  delay?: number
}) {
  return (
    <motion.div
      className={className}
      initial="hidden"
      animate="visible"
      variants={{
        hidden: { opacity: 0 },
        visible: {
          opacity: 1,
          transition: {
            staggerChildren: delay
          }
        }
      }}
    >
      {children}
    </motion.div>
  )
}

export function StaggerItem({ 
  children, 
  className = '' 
}: { 
  children: ReactNode
  className?: string
}) {
  return (
    <motion.div
      className={className}
      variants={{
        hidden: { opacity: 0, y: 20 },
        visible: { opacity: 1, y: 0 }
      }}
      transition={{ duration: 0.4 }}
    >
      {children}
    </motion.div>
  )
}

// Reveal animation (for text/content)
export function RevealText({ 
  children, 
  delay = 0,
  className = '' 
}: { 
  children: ReactNode
  delay?: number
  className?: string
}) {
  return (
    <motion.div
      className={`overflow-hidden ${className}`}
      initial="hidden"
      animate="visible"
      variants={{
        hidden: { height: 0, opacity: 0 },
        visible: { 
          height: 'auto', 
          opacity: 1,
          transition: {
            height: { duration: 0.4 },
            opacity: { duration: 0.25, delay: 0.15 }
          }
        }
      }}
      transition={{ delay }}
    >
      {children}
    </motion.div>
  )
}

// Bounce in animation
export function BounceIn({ 
  children, 
  delay = 0,
  className = '' 
}: { 
  children: ReactNode
  delay?: number
  className?: string
}) {
  return (
    <motion.div
      className={className}
      initial={{ scale: 0, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      transition={{
        type: 'spring',
        stiffness: 260,
        damping: 20,
        delay
      }}
    >
      {children}
    </motion.div>
  )
}

// Typewriter effect
export function TypewriterText({ 
  text, 
  delay = 0,
  speed = 0.05,
  className = '' 
}: { 
  text: string
  delay?: number
  speed?: number
  className?: string
}) {
  return (
    <motion.span
      className={className}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ delay }}
    >
      {text.split('').map((char, index) => (
        <motion.span
          key={index}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: delay + index * speed }}
        >
          {char}
        </motion.span>
      ))}
    </motion.span>
  )
}
