'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { PredictionResult } from '@/lib/prediction'
import { Share2, Facebook, Twitter, Link, Check } from 'lucide-react'

interface ShareButtonsProps {
  prediction: PredictionResult
}

export default function ShareButtons({ prediction }: ShareButtonsProps) {
  const [copied, setCopied] = useState(false)

  const shareToFacebook = () => {
    const url = encodeURIComponent(prediction.shareData.url)
    const text = encodeURIComponent(prediction.shareData.text)
    window.open(
      `https://www.facebook.com/sharer/sharer.php?u=${url}&quote=${text}`,
      '_blank',
      'width=600,height=400'
    )
  }

  const shareToTwitter = () => {
    const text = encodeURIComponent(prediction.shareData.text)
    const url = encodeURIComponent(prediction.shareData.url)
    const hashtags = prediction.shareData.hashtags.join(',')
    window.open(
      `https://twitter.com/intent/tweet?text=${text}&url=${url}&hashtags=${hashtags}`,
      '_blank',
      'width=600,height=400'
    )
  }

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(
        `${prediction.shareData.text} ${prediction.shareData.url}`
      )
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('Failed to copy to clipboard:', err)
    }
  }

  const shareNative = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: 'Snow Day Prediction',
          text: prediction.shareData.text,
          url: prediction.shareData.url,
        })
      } catch (err) {
        console.error('Error sharing:', err)
      }
    } else {
      copyToClipboard()
    }
  }

  return (
    <div className="flex items-center justify-center space-x-2">
      {/* Facebook Share */}
      <motion.button
        onClick={shareToFacebook}
        className="p-2 bg-blue-600 text-white rounded-full hover:bg-blue-700 transition-colors"
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        title="Share on Facebook"
      >
        <Facebook className="w-4 h-4" />
      </motion.button>

      {/* Twitter Share */}
      <motion.button
        onClick={shareToTwitter}
        className="p-2 bg-sky-500 text-white rounded-full hover:bg-sky-600 transition-colors"
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        title="Share on Twitter"
      >
        <Twitter className="w-4 h-4" />
      </motion.button>

      {/* Copy Link */}
      <motion.button
        onClick={copyToClipboard}
        className={`p-2 rounded-full transition-colors ${
          copied 
            ? 'bg-green-600 text-white' 
            : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
        }`}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        title={copied ? 'Copied!' : 'Copy link'}
      >
        {copied ? <Check className="w-4 h-4" /> : <Link className="w-4 h-4" />}
      </motion.button>

      {/* Native Share (Mobile) */}
      {navigator.share && (
        <motion.button
          onClick={shareNative}
          className="p-2 bg-gray-200 text-gray-700 rounded-full hover:bg-gray-300 transition-colors"
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          title="Share"
        >
          <Share2 className="w-4 h-4" />
        </motion.button>
      )}
    </div>
  )
}
