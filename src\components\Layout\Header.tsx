'use client'

import { motion } from 'framer-motion'

export default function Header() {
  return (
    <motion.header 
      className="fixed top-0 left-0 right-0 z-40 bg-white shadow-sm"
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-4">
          {/* Logo and Title */}
          <div className="flex items-center space-x-4">
            <div className="text-2xl font-bold text-orange-600">@A</div>
            <div className="flex flex-col">
              <h1 className="text-xl font-century-gothic font-bold text-gray-800">
                DAS Automatic
              </h1>
              <h2 className="text-lg font-century-gothic text-gray-600">
                Snow Day Calculator®
              </h2>
            </div>
          </div>

          {/* Version and Sign In */}
          <div className="flex items-center space-x-6">
            <div className="text-sm text-gray-500">
              Version 4.3.2.6
            </div>
            <button className="text-sm text-gray-600 hover:text-gray-800 transition-colors">
              Sign in
            </button>
          </div>
        </div>
      </div>
    </motion.header>
  )
}
