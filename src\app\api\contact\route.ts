import { NextRequest, NextResponse } from 'next/server'
import nodemailer from 'nodemailer'

interface ContactFormData {
  name: string
  email: string
  zipCode?: string
  message: string
  category: string
}

export async function POST(request: NextRequest) {
  try {
    const body: ContactFormData = await request.json()
    const { name, email, zipCode, message, category } = body

    // Validate required fields
    if (!name || !email || !message) {
      return NextResponse.json(
        { error: 'Name, email, and message are required' },
        { status: 400 }
      )
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      )
    }

    // Validate message length
    if (message.trim().length < 10) {
      return NextResponse.json(
        { error: 'Message must be at least 10 characters long' },
        { status: 400 }
      )
    }

    // Create email content
    const emailContent = `
      New contact form submission from Snow Day Calculator:
      
      Name: ${name}
      Email: ${email}
      Zip Code: ${zipCode || 'Not provided'}
      Category: ${category}
      
      Message:
      ${message}
      
      ---
      Submitted at: ${new Date().toLocaleString()}
      IP Address: ${request.headers.get('x-forwarded-for') || 'Unknown'}
      User Agent: ${request.headers.get('user-agent') || 'Unknown'}
    `

    // Send email if SMTP is configured
    if (process.env.SMTP_HOST && process.env.SMTP_USER && process.env.SMTP_PASS) {
      try {
        const transporter = nodemailer.createTransporter({
          host: process.env.SMTP_HOST,
          port: parseInt(process.env.SMTP_PORT || '587'),
          secure: process.env.SMTP_PORT === '465',
          auth: {
            user: process.env.SMTP_USER,
            pass: process.env.SMTP_PASS,
          },
        })

        // Send email to admin
        await transporter.sendMail({
          from: process.env.SMTP_USER,
          to: process.env.SMTP_USER, // Send to yourself
          subject: `Snow Day Calculator Contact: ${category} from ${name}`,
          text: emailContent,
          replyTo: email,
        })

        // Send confirmation email to user
        await transporter.sendMail({
          from: process.env.SMTP_USER,
          to: email,
          subject: 'Thank you for contacting Snow Day Calculator',
          text: `Hi ${name},

Thank you for your message! We've received your ${category.toLowerCase()} and will get back to you as soon as possible.

Your message:
"${message}"

Best regards,
Snow Day Calculator Team

---
This is an automated response. Please do not reply to this email.`,
        })

        console.log('Contact form email sent successfully')
      } catch (emailError) {
        console.error('Failed to send email:', emailError)
        // Continue without failing the request - we'll still log the submission
      }
    } else {
      console.warn('SMTP not configured, email not sent')
    }

    // Log the submission (in a real app, you'd save this to a database)
    console.log('Contact form submission:', {
      name,
      email,
      zipCode,
      category,
      messageLength: message.length,
      timestamp: new Date().toISOString(),
      ip: request.headers.get('x-forwarded-for'),
    })

    // Return success response
    return NextResponse.json({
      success: true,
      message: 'Your message has been sent successfully!',
      timestamp: new Date().toISOString(),
    })

  } catch (error) {
    console.error('Contact form error:', error)
    return NextResponse.json(
      { 
        error: 'Failed to send message',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// GET endpoint for contact form configuration
export async function GET() {
  return NextResponse.json({
    success: true,
    data: {
      categories: [
        { value: 'feedback', label: 'General Feedback' },
        { value: 'bug', label: 'Bug Report' },
        { value: 'feature', label: 'Feature Request' },
        { value: 'prediction', label: 'Prediction Question' },
        { value: 'technical', label: 'Technical Issue' },
        { value: 'other', label: 'Other' }
      ],
      emailConfigured: !!(process.env.SMTP_HOST && process.env.SMTP_USER && process.env.SMTP_PASS)
    }
  })
}
