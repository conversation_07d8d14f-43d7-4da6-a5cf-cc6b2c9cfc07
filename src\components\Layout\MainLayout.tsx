'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import Navigation from './Navigation'
import MobileNavigation from './MobileNavigation'
import Header from './Header'
import Footer from './Footer'

interface MainLayoutProps {
  children: React.ReactNode
}

export default function MainLayout({ children }: MainLayoutProps) {
  const [activeSection, setActiveSection] = useState('calculator')

  const pageVariants = {
    initial: { 
      opacity: 0, 
      x: 100,
      width: '64px'
    },
    in: { 
      opacity: 1, 
      x: 0,
      width: '704px'
    },
    out: { 
      opacity: 0, 
      x: -100,
      width: '64px'
    }
  }

  const pageTransition = {
    type: 'tween',
    ease: 'anticipate',
    duration: 0.6
  }

  return (
    <div className="min-h-screen bg-gray-100">
      <Header />

      {/* Desktop Navigation */}
      <div className="hidden md:block">
        <Navigation
          activeSection={activeSection}
          onSectionChange={setActiveSection}
        />
      </div>

      {/* Mobile Navigation */}
      <MobileNavigation
        activeSection={activeSection}
        onSectionChange={setActiveSection}
      />

      <main className="md:ml-20 pt-20 pb-10 px-4 md:px-0">
        <div className="max-w-4xl mx-auto">
          <AnimatePresence mode="wait">
            <motion.div
              key={activeSection}
              initial="initial"
              animate="in"
              exit="out"
              variants={pageVariants}
              transition={pageTransition}
              className="bg-white rounded-lg shadow-lg overflow-hidden"
            >
              {children}
            </motion.div>
          </AnimatePresence>
        </div>
      </main>

      <Footer />
    </div>
  )
}
