'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'

interface CalculatorFormProps {
  onSubmit: (data: FormData) => void
  loading?: boolean
}

export interface FormData {
  zipCode: string
  snowDays: number
  schoolType: string
  extraFactors: number
}

const schoolTypes = [
  { value: 'public', label: 'Public' },
  { value: 'urban-public', label: 'Urban Public' },
  { value: 'rural-public', label: 'Rural Public' },
  { value: 'private', label: 'Private/Prep' },
  { value: 'boarding', label: 'Boarding' },
]

export default function CalculatorForm({ onSubmit, loading = false }: CalculatorFormProps) {
  const [formData, setFormData] = useState<FormData>({
    zipCode: '',
    snowDays: 0,
    schoolType: 'public',
    extraFactors: 0,
  })

  const [errors, setErrors] = useState<Partial<FormData>>({})

  const validateForm = (): boolean => {
    const newErrors: Partial<FormData> = {}

    // Validate zip code (US format: 5 digits or Canadian format: A1A 1A1)
    const zipRegex = /^(\d{5}|[A-Za-z]\d[A-Za-z] \d[A-Za-z]\d)$/
    if (!formData.zipCode) {
      newErrors.zipCode = 'Zip code is required'
    } else if (!zipRegex.test(formData.zipCode)) {
      newErrors.zipCode = 'Please enter a valid US or Canadian zip code'
    }

    // Validate snow days
    if (formData.snowDays < 0 || formData.snowDays > 20) {
      newErrors.snowDays = 'Snow days must be between 0 and 20'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (validateForm()) {
      onSubmit(formData)
    }
  }

  const handleInputChange = (field: keyof FormData, value: string | number) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="p-8"
    >
      <div className="text-center mb-8">
        <h2 className="text-3xl font-century-gothic font-bold text-gray-800 mb-4">
          Snow Day Calculator
        </h2>
        <p className="text-gray-600">
          Enter your information below to get your snow day prediction
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Zip Code Input */}
        <div>
          <label htmlFor="zipCode" className="block text-sm font-medium text-gray-700 mb-2">
            Please Enter Your Zip Code:
          </label>
          <input
            type="text"
            id="zipCode"
            className={`form-input ${errors.zipCode ? 'border-red-500' : ''}`}
            placeholder="Enter US or Canadian Zip Code"
            value={formData.zipCode}
            onChange={(e) => handleInputChange('zipCode', e.target.value.toUpperCase())}
            maxLength={7}
          />
          {errors.zipCode && (
            <p className="mt-1 text-sm text-red-600">{errors.zipCode}</p>
          )}
          <p className="mt-1 text-xs text-gray-500">
            Change to full keyboard for Canadian postal codes
          </p>
        </div>

        {/* Snow Days Input */}
        <div>
          <label htmlFor="snowDays" className="block text-sm font-medium text-gray-700 mb-2">
            Snow Days this year:
          </label>
          <input
            type="number"
            id="snowDays"
            className={`form-input ${errors.snowDays ? 'border-red-500' : ''}`}
            placeholder="0"
            min="0"
            max="20"
            value={formData.snowDays}
            onChange={(e) => handleInputChange('snowDays', parseInt(e.target.value) || 0)}
          />
          {errors.snowDays && (
            <p className="mt-1 text-sm text-red-600">{errors.snowDays}</p>
          )}
        </div>

        {/* School Type Selection */}
        <div>
          <label htmlFor="schoolType" className="block text-sm font-medium text-gray-700 mb-2">
            Type of School:
          </label>
          <select
            id="schoolType"
            className="form-select"
            value={formData.schoolType}
            onChange={(e) => handleInputChange('schoolType', e.target.value)}
          >
            {schoolTypes.map((type) => (
              <option key={type.value} value={type.value}>
                {type.label}
              </option>
            ))}
          </select>
        </div>

        {/* Extra Factors */}
        <div>
          <label htmlFor="extraFactors" className="block text-sm font-medium text-gray-700 mb-2">
            Extra Factors (Optional):
          </label>
          <input
            type="number"
            id="extraFactors"
            className="form-input"
            placeholder="0"
            min="-3"
            max="3"
            value={formData.extraFactors}
            onChange={(e) => handleInputChange('extraFactors', parseInt(e.target.value) || 0)}
          />
          <p className="mt-1 text-xs text-gray-500">
            Range: -3 to 3 (lower values increase snow day chances)
          </p>
        </div>

        {/* Submit Button */}
        <div className="text-center pt-4">
          <motion.button
            type="submit"
            disabled={loading}
            className={`btn-primary w-full md:w-auto ${loading ? 'opacity-50 cursor-not-allowed' : ''}`}
            whileHover={{ scale: loading ? 1 : 1.05 }}
            whileTap={{ scale: loading ? 1 : 0.95 }}
          >
            {loading ? (
              <div className="flex items-center justify-center space-x-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Calculating...</span>
              </div>
            ) : (
              'Calculate Snow Day Chance'
            )}
          </motion.button>
        </div>
      </form>

      {/* Information Box */}
      <motion.div
        className="mt-8 p-4 bg-blue-50 rounded-lg border border-blue-200"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.3 }}
      >
        <div className="text-sm text-blue-800">
          <p className="font-semibold mb-2">How it works:</p>
          <ul className="space-y-1">
            <li>• Based on the Amount, Time of, Length and Chance of Snow</li>
            <li>• Gets Weather Information from Weather.gov</li>
            <li>• The Best and Only Automatic Snow-Day Predictor out there</li>
            <li>• Uses the most recent weather information possible</li>
            <li>• Predictions for the next two days go up at 12:00pm daily</li>
          </ul>
        </div>
      </motion.div>
    </motion.div>
  )
}
