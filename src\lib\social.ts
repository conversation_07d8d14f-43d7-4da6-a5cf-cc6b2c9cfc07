// Social media sharing utilities

export interface SocialShareData {
  url: string
  title: string
  description: string
  image?: string
  hashtags?: string[]
}

export class SocialMediaService {
  // Generate Open Graph meta tags for better social sharing
  static generateMetaTags(shareData: SocialShareData): string {
    return `
      <meta property="og:title" content="${shareData.title}" />
      <meta property="og:description" content="${shareData.description}" />
      <meta property="og:url" content="${shareData.url}" />
      <meta property="og:type" content="website" />
      ${shareData.image ? `<meta property="og:image" content="${shareData.image}" />` : ''}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content="${shareData.title}" />
      <meta name="twitter:description" content="${shareData.description}" />
      ${shareData.image ? `<meta name="twitter:image" content="${shareData.image}" />` : ''}
    `
  }

  // Share to Facebook
  static shareToFacebook(shareData: SocialShareData): void {
    const url = encodeURIComponent(shareData.url)
    const quote = encodeURIComponent(shareData.description)
    
    const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${url}&quote=${quote}`
    
    this.openShareWindow(facebookUrl, 'Facebook Share')
  }

  // Share to Twitter
  static shareToTwitter(shareData: SocialShareData): void {
    const text = encodeURIComponent(shareData.description)
    const url = encodeURIComponent(shareData.url)
    const hashtags = shareData.hashtags ? shareData.hashtags.join(',') : ''
    
    const twitterUrl = `https://twitter.com/intent/tweet?text=${text}&url=${url}${hashtags ? `&hashtags=${hashtags}` : ''}`
    
    this.openShareWindow(twitterUrl, 'Twitter Share')
  }

  // Share to LinkedIn
  static shareToLinkedIn(shareData: SocialShareData): void {
    const url = encodeURIComponent(shareData.url)
    const title = encodeURIComponent(shareData.title)
    const summary = encodeURIComponent(shareData.description)
    
    const linkedInUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${url}&title=${title}&summary=${summary}`
    
    this.openShareWindow(linkedInUrl, 'LinkedIn Share')
  }

  // Copy to clipboard
  static async copyToClipboard(shareData: SocialShareData): Promise<boolean> {
    try {
      const textToCopy = `${shareData.description} ${shareData.url}`
      await navigator.clipboard.writeText(textToCopy)
      return true
    } catch (error) {
      console.error('Failed to copy to clipboard:', error)
      return false
    }
  }

  // Native share API (mobile)
  static async shareNative(shareData: SocialShareData): Promise<boolean> {
    if (!navigator.share) {
      return false
    }

    try {
      await navigator.share({
        title: shareData.title,
        text: shareData.description,
        url: shareData.url,
      })
      return true
    } catch (error) {
      console.error('Native share failed:', error)
      return false
    }
  }

  // Generate shareable image URL (for future implementation)
  static generateShareImage(prediction: {
    percentage: number
    zipCode: string
    dayOfWeek: string
  }): string {
    // This would generate a dynamic image with the prediction data
    // For now, return a placeholder
    const baseUrl = typeof window !== 'undefined' ? window.location.origin : ''
    return `${baseUrl}/api/share-image?percentage=${prediction.percentage}&zipCode=${prediction.zipCode}&day=${prediction.dayOfWeek}`
  }

  // Private helper to open share windows
  private static openShareWindow(url: string, title: string): void {
    const width = 600
    const height = 400
    const left = (window.screen.width - width) / 2
    const top = (window.screen.height - height) / 2
    
    window.open(
      url,
      title,
      `width=${width},height=${height},left=${left},top=${top},resizable=yes,scrollbars=yes`
    )
  }

  // Generate prediction-specific share data
  static generatePredictionShareData(
    prediction: {
      percentage: number
      dayOfWeek: string
      message: string
    },
    zipCode: string
  ): SocialShareData {
    const baseUrl = typeof window !== 'undefined' ? window.location.origin : ''
    const url = `${baseUrl}/prediction?zipcode=${zipCode}&prediction=${prediction.percentage}`
    
    const title = `Snow Day Prediction for ${zipCode}`
    const description = `The Snow Day Calculator says there is a ${prediction.percentage}% chance of a snow day this ${prediction.dayOfWeek} for ${zipCode}! ${prediction.message}`
    
    return {
      url,
      title,
      description,
      image: this.generateShareImage({
        percentage: prediction.percentage,
        zipCode,
        dayOfWeek: prediction.dayOfWeek
      }),
      hashtags: ['SnowDay', 'Weather', 'School', 'SnowDayCalculator']
    }
  }
}

// Hook for social sharing
export function useSocialShare() {
  const shareToFacebook = (shareData: SocialShareData) => {
    SocialMediaService.shareToFacebook(shareData)
  }

  const shareToTwitter = (shareData: SocialShareData) => {
    SocialMediaService.shareToTwitter(shareData)
  }

  const shareToLinkedIn = (shareData: SocialShareData) => {
    SocialMediaService.shareToLinkedIn(shareData)
  }

  const copyToClipboard = async (shareData: SocialShareData): Promise<boolean> => {
    return await SocialMediaService.copyToClipboard(shareData)
  }

  const shareNative = async (shareData: SocialShareData): Promise<boolean> => {
    return await SocialMediaService.shareNative(shareData)
  }

  return {
    shareToFacebook,
    shareToTwitter,
    shareToLinkedIn,
    copyToClipboard,
    shareNative,
  }
}
