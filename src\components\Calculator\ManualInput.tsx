'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'

interface ManualInputProps {
  onSubmit: (data: ManualWeatherData) => void
  onBack: () => void
  loading?: boolean
}

export interface ManualWeatherData {
  temperature: number
  snowfall: number
  windSpeed: number
  iceConditions: boolean
  stormDuration: number
  stormTiming: 'before-school' | 'during-school' | 'after-school' | 'overnight'
}

export default function ManualInput({ onSubmit, onBack, loading = false }: ManualInputProps) {
  const [weatherData, setWeatherData] = useState<ManualWeatherData>({
    temperature: 32,
    snowfall: 0,
    windSpeed: 0,
    iceConditions: false,
    stormDuration: 0,
    stormTiming: 'overnight',
  })

  const [errors, setErrors] = useState<Partial<ManualWeatherData>>({})

  const validateForm = (): boolean => {
    const newErrors: Partial<ManualWeatherData> = {}

    if (weatherData.temperature < -50 || weatherData.temperature > 100) {
      newErrors.temperature = 'Temperature must be between -50°F and 100°F'
    }

    if (weatherData.snowfall < 0 || weatherData.snowfall > 50) {
      newErrors.snowfall = 'Snowfall must be between 0 and 50 inches'
    }

    if (weatherData.windSpeed < 0 || weatherData.windSpeed > 100) {
      newErrors.windSpeed = 'Wind speed must be between 0 and 100 mph'
    }

    if (weatherData.stormDuration < 0 || weatherData.stormDuration > 48) {
      newErrors.stormDuration = 'Storm duration must be between 0 and 48 hours'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (validateForm()) {
      onSubmit(weatherData)
    }
  }

  const handleInputChange = (field: keyof ManualWeatherData, value: any) => {
    setWeatherData(prev => ({ ...prev, [field]: value }))
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.5 }}
      className="p-8"
    >
      <div className="text-center mb-8">
        <h2 className="text-3xl font-century-gothic font-bold text-gray-800 mb-4">
          Manual Weather Input
        </h2>
        <p className="text-gray-600">
          Enter weather data manually for more precise predictions
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Temperature */}
        <div>
          <label htmlFor="temperature" className="block text-sm font-medium text-gray-700 mb-2">
            Temperature (°F):
          </label>
          <input
            type="number"
            id="temperature"
            className={`form-input ${errors.temperature ? 'border-red-500' : ''}`}
            placeholder="32"
            value={weatherData.temperature}
            onChange={(e) => handleInputChange('temperature', parseInt(e.target.value) || 0)}
          />
          {errors.temperature && (
            <p className="mt-1 text-sm text-red-600">{errors.temperature}</p>
          )}
        </div>

        {/* Snowfall */}
        <div>
          <label htmlFor="snowfall" className="block text-sm font-medium text-gray-700 mb-2">
            Expected Snowfall (inches):
          </label>
          <input
            type="number"
            id="snowfall"
            step="0.1"
            className={`form-input ${errors.snowfall ? 'border-red-500' : ''}`}
            placeholder="0"
            value={weatherData.snowfall}
            onChange={(e) => handleInputChange('snowfall', parseFloat(e.target.value) || 0)}
          />
          {errors.snowfall && (
            <p className="mt-1 text-sm text-red-600">{errors.snowfall}</p>
          )}
        </div>

        {/* Wind Speed */}
        <div>
          <label htmlFor="windSpeed" className="block text-sm font-medium text-gray-700 mb-2">
            Wind Speed (mph):
          </label>
          <input
            type="number"
            id="windSpeed"
            className={`form-input ${errors.windSpeed ? 'border-red-500' : ''}`}
            placeholder="0"
            value={weatherData.windSpeed}
            onChange={(e) => handleInputChange('windSpeed', parseInt(e.target.value) || 0)}
          />
          {errors.windSpeed && (
            <p className="mt-1 text-sm text-red-600">{errors.windSpeed}</p>
          )}
        </div>

        {/* Storm Duration */}
        <div>
          <label htmlFor="stormDuration" className="block text-sm font-medium text-gray-700 mb-2">
            Storm Duration (hours):
          </label>
          <input
            type="number"
            id="stormDuration"
            className={`form-input ${errors.stormDuration ? 'border-red-500' : ''}`}
            placeholder="0"
            value={weatherData.stormDuration}
            onChange={(e) => handleInputChange('stormDuration', parseInt(e.target.value) || 0)}
          />
          {errors.stormDuration && (
            <p className="mt-1 text-sm text-red-600">{errors.stormDuration}</p>
          )}
        </div>

        {/* Storm Timing */}
        <div>
          <label htmlFor="stormTiming" className="block text-sm font-medium text-gray-700 mb-2">
            Storm Timing:
          </label>
          <select
            id="stormTiming"
            className="form-select"
            value={weatherData.stormTiming}
            onChange={(e) => handleInputChange('stormTiming', e.target.value)}
          >
            <option value="overnight">Overnight (Best for snow days)</option>
            <option value="before-school">Before School Hours</option>
            <option value="during-school">During School Hours</option>
            <option value="after-school">After School Hours</option>
          </select>
        </div>

        {/* Ice Conditions */}
        <div>
          <label className="flex items-center space-x-3">
            <input
              type="checkbox"
              checked={weatherData.iceConditions}
              onChange={(e) => handleInputChange('iceConditions', e.target.checked)}
              className="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
            />
            <span className="text-sm font-medium text-gray-700">
              Ice conditions expected (increases snow day chances)
            </span>
          </label>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 pt-4">
          <motion.button
            type="button"
            onClick={onBack}
            className="btn-secondary flex-1"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            ← Back to Automatic
          </motion.button>
          
          <motion.button
            type="submit"
            disabled={loading}
            className={`btn-primary flex-1 ${loading ? 'opacity-50 cursor-not-allowed' : ''}`}
            whileHover={{ scale: loading ? 1 : 1.05 }}
            whileTap={{ scale: loading ? 1 : 0.95 }}
          >
            {loading ? (
              <div className="flex items-center justify-center space-x-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Calculating...</span>
              </div>
            ) : (
              'Calculate with Manual Data'
            )}
          </motion.button>
        </div>
      </form>

      {/* Information Box */}
      <motion.div
        className="mt-8 p-4 bg-yellow-50 rounded-lg border border-yellow-200"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.3 }}
      >
        <div className="text-sm text-yellow-800">
          <p className="font-semibold mb-2">Manual Input Tips:</p>
          <ul className="space-y-1">
            <li>• More accurate for current or ongoing storms</li>
            <li>• Use when automatic data seems incorrect</li>
            <li>• Overnight storms have the highest snow day probability</li>
            <li>• Ice conditions significantly increase closure chances</li>
          </ul>
        </div>
      </motion.div>
    </motion.div>
  )
}
