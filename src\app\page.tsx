'use client'

import { useState } from 'react'
import MainLayout from '@/components/Layout/MainLayout'
import CalculatorForm, { FormData } from '@/components/Calculator/CalculatorForm'
import ManualInput, { ManualWeatherData } from '@/components/Calculator/ManualInput'
import PredictionDisplay from '@/components/Results/PredictionDisplay'
import ContactForm from '@/components/Contact/ContactForm'
import { PredictionLoadingAnimation } from '@/components/UI/LoadingSpinner'
import { PredictionResult } from '@/lib/prediction'
import { WeatherData } from '@/lib/weather'

type ViewMode = 'auto' | 'manual' | 'results' | 'about' | 'apps' | 'services' | 'contact'

export default function HomePage() {
  const [viewMode, setViewMode] = useState<ViewMode>('auto')
  const [loading, setLoading] = useState(false)
  const [predictions, setPredictions] = useState<PredictionResult[]>([])
  const [weatherData, setWeatherData] = useState<WeatherData | null>(null)
  const [currentZipCode, setCurrentZipCode] = useState('')

  const handleFormSubmit = async (formData: FormData) => {
    setLoading(true)
    setCurrentZipCode(formData.zipCode)

    try {
      const response = await fetch('/api/predict', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ formData }),
      })

      if (!response.ok) {
        throw new Error('Failed to get prediction')
      }

      const result = await response.json()
      setPredictions(result.data.predictions)
      setWeatherData(result.data.weatherData)
      setViewMode('results')
    } catch (error) {
      console.error('Error getting prediction:', error)
      alert('Failed to get prediction. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleManualSubmit = async (manualData: ManualWeatherData) => {
    setLoading(true)

    try {
      // For manual input, we need a zip code from the previous form
      // In a real implementation, you'd store this from the previous step
      const formData: FormData = {
        zipCode: currentZipCode || '60007', // Default for demo
        snowDays: 0,
        schoolType: 'public',
        extraFactors: 0
      }

      const response = await fetch('/api/predict', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ formData, manualData }),
      })

      if (!response.ok) {
        throw new Error('Failed to get prediction')
      }

      const result = await response.json()
      setPredictions(result.data.predictions)
      setWeatherData(result.data.weatherData)
      setViewMode('results')
    } catch (error) {
      console.error('Error getting prediction:', error)
      alert('Failed to get prediction. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleNewPrediction = () => {
    setPredictions([])
    setWeatherData(null)
    setCurrentZipCode('')
    setViewMode('auto')
  }

  const renderContent = () => {
    if (loading) {
      return <PredictionLoadingAnimation />
    }

    switch (viewMode) {
      case 'auto':
        return (
          <div>
            <CalculatorForm onSubmit={handleFormSubmit} loading={loading} />
            <div className="text-center mt-4">
              <button
                onClick={() => setViewMode('manual')}
                className="text-orange-600 hover:text-orange-700 underline"
              >
                « Enter Data Manually »
              </button>
            </div>
          </div>
        )

      case 'manual':
        return (
          <ManualInput
            onSubmit={handleManualSubmit}
            onBack={() => setViewMode('auto')}
            loading={loading}
          />
        )

      case 'results':
        return predictions.length > 0 && weatherData ? (
          <PredictionDisplay
            predictions={predictions}
            weatherData={weatherData}
            zipCode={currentZipCode}
            onNewPrediction={handleNewPrediction}
          />
        ) : (
          <div className="p-8 text-center">
            <p className="text-gray-600">No predictions available.</p>
            <button
              onClick={handleNewPrediction}
              className="mt-4 btn-primary"
            >
              Start New Prediction
            </button>
          </div>
        )

      case 'about':
        return (
          <div className="p-8">
            <h2 className="text-3xl font-bold text-gray-800 mb-6">About Snow Day Calculator</h2>
            <div className="prose max-w-none">
              <p className="mb-4">
                The Snow Day Calculator was started as a middle school side project in 2007 to predict the chance of school closings. 
                This was a unique service where users could enter weather information and The Snow Day Calculator would output the 
                likelihood of a snow day the next day.
              </p>
              <p className="mb-4">
                In 2010, Snow Day Calculator launched automatic data retrieval from the National Weather Service making The Snow Day 
                Calculator able to automatically predict for any US zip code. Today, over 5 million people come to the tool every year 
                to get accurate info about if their school will be closed due to the coming weather.
              </p>
              <p className="mb-4">
                Snow day predictions use the timing and strength of a snowstorm, wind, temperature, ice forecasts, and historical 
                information about a user's location and school. Predictions are highly accurate and are trusted by millions of users 
                all across the country.
              </p>
            </div>
          </div>
        )

      case 'apps':
        return (
          <div className="p-8">
            <h2 className="text-3xl font-bold text-gray-800 mb-6">Mobile Apps</h2>
            <div className="text-center">
              <p className="text-gray-600 mb-6">
                Get snow day predictions on the go with our mobile apps!
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <div className="bg-gray-100 p-6 rounded-lg">
                  <h3 className="font-semibold mb-2">iOS App</h3>
                  <p className="text-sm text-gray-600">Coming Soon</p>
                </div>
                <div className="bg-gray-100 p-6 rounded-lg">
                  <h3 className="font-semibold mb-2">Android App</h3>
                  <p className="text-sm text-gray-600">Coming Soon</p>
                </div>
              </div>
            </div>
          </div>
        )

      case 'services':
        return (
          <div className="p-8">
            <h2 className="text-3xl font-bold text-gray-800 mb-6">Services</h2>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-blue-50 p-6 rounded-lg">
                <h3 className="font-semibold text-blue-800 mb-2">Text Notifications</h3>
                <p className="text-blue-700 text-sm">
                  Get snow day predictions sent directly to your phone via text message.
                </p>
              </div>
              <div className="bg-green-50 p-6 rounded-lg">
                <h3 className="font-semibold text-green-800 mb-2">School District Tools</h3>
                <p className="text-green-700 text-sm">
                  Administrative tools for school districts to manage closings and announcements.
                </p>
              </div>
            </div>
          </div>
        )

      case 'contact':
        return <ContactForm />

      default:
        return <CalculatorForm onSubmit={handleFormSubmit} loading={loading} />
    }
  }

  return (
    <MainLayout>
      {renderContent()}
    </MainLayout>
  )
}
