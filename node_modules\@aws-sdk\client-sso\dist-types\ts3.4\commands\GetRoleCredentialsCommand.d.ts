import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  GetRoleCredentialsRequest,
  GetRoleCredentialsResponse,
} from "../models/models_0";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SSOClientResolvedConfig,
} from "../SSOClient";
export { __MetadataBearer };
export { $Command };
export interface GetRoleCredentialsCommandInput
  extends GetRoleCredentialsRequest {}
export interface GetRoleCredentialsCommandOutput
  extends GetRoleCredentialsResponse,
    __MetadataBearer {}
declare const GetRoleCredentialsCommand_base: {
  new (
    input: GetRoleCredentialsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetRoleCredentialsCommandInput,
    GetRoleCredentialsCommandOutput,
    SSOClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: GetRoleCredentialsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetRoleCredentialsCommandInput,
    GetRoleCredentialsCommandOutput,
    SSOClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class GetRoleCredentialsCommand extends GetRoleCredentialsCommand_base {
  protected static __types: {
    api: {
      input: GetRoleCredentialsRequest;
      output: GetRoleCredentialsResponse;
    };
    sdk: {
      input: GetRoleCredentialsCommandInput;
      output: GetRoleCredentialsCommandOutput;
    };
  };
}
