/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        'snow-orange': '#fc0',
        'snow-orange-dark': '#f60',
        'snow-gray': '#c9bea7',
        'snow-dark': '#554e43',
        'snow-bg': '#2A0200',
      },
      fontFamily: {
        'century-gothic': ['Century Gothic', 'sans-serif'],
      },
      backgroundImage: {
        'orange-gradient': 'linear-gradient(#fc0, #f60)',
        'gray-gradient': 'linear-gradient(#b2b2b2, #5f5f5f)',
      },
      animation: {
        'slide-in': 'slideIn 0.6s ease-out',
        'fade-in': 'fadeIn 0.5s ease-in',
      },
      keyframes: {
        slideIn: {
          '0%': { transform: 'translateX(-100%)', opacity: '0' },
          '100%': { transform: 'translateX(0)', opacity: '1' },
        },
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
      },
    },
  },
  plugins: [],
}
