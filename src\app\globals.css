@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom scrollbar styles */
::-webkit-scrollbar {
  display: none;
}

/* Century Gothic font fallback */
.font-century-gothic {
  font-family: 'Century Gothic', 'Futura', 'Arial', sans-serif;
}

/* Custom animations */
@keyframes slideIn {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Navigation button styles */
.nav-button {
  @apply relative w-14 h-[459px] bg-gray-gradient rounded-xl transition-all duration-300;
}

.nav-button.active {
  @apply bg-orange-gradient;
}

.nav-button::before {
  content: attr(data-label);
  @apply absolute bottom-6 left-1/2 transform -translate-x-1/2 rotate-90 text-white font-century-gothic text-4xl font-normal tracking-wide;
}

.nav-button::after {
  content: attr(data-label);
  @apply absolute top-2 left-1/2 transform -translate-x-1/2 text-white font-century-gothic text-xs font-bold tracking-widest uppercase;
}

/* Form styles */
.form-input {
  @apply w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-transparent;
}

.form-select {
  @apply w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-transparent bg-white;
}

.btn-primary {
  @apply bg-orange-gradient text-white px-6 py-3 rounded-md font-semibold hover:shadow-lg transition-all duration-300;
}

.btn-secondary {
  @apply bg-gray-200 text-gray-800 px-6 py-3 rounded-md font-semibold hover:bg-gray-300 transition-all duration-300;
}

/* Result display styles */
.prediction-card {
  @apply bg-white rounded-lg shadow-lg p-6 border-l-4 border-orange-500;
}

.percentage-display {
  @apply text-6xl font-bold text-orange-600 font-century-gothic;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  .nav-button {
    @apply w-12 h-20 mb-2;
  }

  .nav-button::before {
    @apply text-sm bottom-1 transform -translate-x-1/2 -rotate-90;
    font-size: 10px;
  }

  .nav-button::after {
    @apply text-xs top-1;
    font-size: 8px;
  }

  .percentage-display {
    @apply text-4xl;
  }

  .prediction-card {
    @apply p-4;
  }

  .form-input, .form-select {
    @apply text-base; /* Prevent zoom on iOS */
  }
}

@media (max-width: 640px) {
  .nav-button {
    @apply w-10 h-16 mb-1;
  }

  .nav-button::before {
    font-size: 8px;
  }

  .nav-button::after {
    font-size: 6px;
  }

  .percentage-display {
    @apply text-3xl;
  }
}

/* Tablet adjustments */
@media (min-width: 769px) and (max-width: 1024px) {
  .nav-button {
    @apply w-13 h-96;
  }

  .nav-button::before {
    @apply text-2xl;
  }

  .nav-button::after {
    @apply text-sm;
  }
}

/* Touch-friendly improvements */
@media (hover: none) and (pointer: coarse) {
  .btn-primary, .btn-secondary {
    @apply py-4 px-8 text-lg;
  }

  .form-input, .form-select {
    @apply py-4 text-lg;
  }

  .nav-button {
    @apply active:scale-95;
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .percentage-display {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}
